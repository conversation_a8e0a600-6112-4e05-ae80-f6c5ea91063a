# AI Hedge Fund - Extractor App Integration

This document explains how to integrate the AI Hedge Fund application with the Extractor App for centralized credential management.

## Overview

The integration allows the AI Hedge Fund app to fetch API credentials from the Extractor App instead of using local environment variables. This provides:

- **Centralized Management**: All credentials managed in one place
- **Enhanced Security**: Encrypted storage with web interface
- **Easy Updates**: Change credentials without redeploying
- **Fallback Support**: Automatic fallback to local environment variables
- **Multi-Provider Support**: OpenAI, Anthropic, Groq, Google, DeepSeek, Financial Datasets

## Prerequisites

1. **Extractor App Running**: The extractor app must be running and accessible
2. **AI Hedge Fund Credentials**: Configured in the extractor app web interface
3. **User Account**: Valid username/password for the extractor app

## Quick Setup

### 1. Run the Setup Script

```bash
cd ai-hedge-fund
python setup_extractor_integration.py
```

The script will guide you through:
- Extractor app connection details
- Credential selection method
- Fallback configuration
- Testing the connection

### 2. Install Dependencies

```bash
poetry install
```

### 3. Test the Integration

```bash
python test_extractor_integration.py
```

## Manual Configuration

If you prefer to configure manually, create/update your `.env` file:

```bash
# Extractor App Configuration
EXTRACTOR_URL=http://localhost:8000
EXTRACTOR_USERNAME=your_username
EXTRACTOR_PASSWORD=your_password

# Credential Selection (choose one method)
EXTRACTOR_CREDENTIAL_ID=1                    # Use specific credential ID
# EXTRACTOR_CREDENTIAL_NAME=Production       # Or use credential name

# Fallback Configuration
USE_LOCAL_FALLBACK=true                      # Use local env vars as fallback
CREDENTIALS_CACHE_TIMEOUT=300                # Cache timeout in seconds

# Local Fallback Credentials (optional)
ANTHROPIC_API_KEY=your-anthropic-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key
GROQ_API_KEY=your-groq-api-key
GOOGLE_API_KEY=your-google-api-key
OPENAI_API_KEY=your-openai-api-key
FINANCIAL_DATASETS_API_KEY=your-financial-datasets-api-key
OPENAI_API_BASE=your-custom-openai-base-url
```

## How It Works

### 1. Credentials Client

The `CredentialsClient` class handles:
- Authentication with the extractor app
- Fetching credentials via API
- Caching for performance
- Fallback to local environment variables

### 2. Model Integration

All LLM models automatically use the credentials client:
- `get_model()` function updated to fetch API keys
- Supports all providers: OpenAI, Anthropic, Groq, Google, DeepSeek
- Automatic fallback to environment variables

### 3. Financial Data Integration

Financial data API calls use centralized credentials:
- `_get_financial_api_key()` function fetches from extractor
- All financial data functions updated
- Seamless fallback support

## Configuration Options

### Credential Selection

**Method 1: Credential ID**
```bash
EXTRACTOR_CREDENTIAL_ID=1
```
Use a specific credential set by ID.

**Method 2: Credential Name**
```bash
EXTRACTOR_CREDENTIAL_NAME=Production
```
Use a credential set by name.

**Method 3: Auto-Select**
Leave both empty to use the first active credential set.

### Caching

```bash
CREDENTIALS_CACHE_TIMEOUT=300  # 5 minutes
```
Credentials are cached to reduce API calls. Set to 0 to disable caching.

### Fallback Behavior

```bash
USE_LOCAL_FALLBACK=true
```
When enabled, the app will use local environment variables if:
- Extractor app is unreachable
- Authentication fails
- No credentials found

## API Reference

### CredentialsClient

```python
from src.utils.credentials_client import get_credentials_client

client = get_credentials_client()

# Get all credentials
credentials = client.get_credentials()

# Get specific provider API key
openai_key = client.get_api_key('openai')
anthropic_key = client.get_api_key('anthropic')

# Get OpenAI base URL
base_url = client.get_openai_base_url()

# Force refresh
client.refresh_credentials()
```

### Convenience Functions

```python
from src.utils.credentials_client import get_api_key

# Quick access to API keys
openai_key = get_api_key('openai')
```

## Troubleshooting

### Connection Issues

**Problem**: Cannot connect to extractor app
**Solutions**:
- Check `EXTRACTOR_URL` is correct
- Verify extractor app is running
- Check network connectivity
- Ensure firewall allows connections

### Authentication Issues

**Problem**: Authentication failed
**Solutions**:
- Verify `EXTRACTOR_USERNAME` and `EXTRACTOR_PASSWORD`
- Check user account exists in extractor app
- Ensure user has proper permissions

### No Credentials Found

**Problem**: No AI hedge fund credentials found
**Solutions**:
- Create credentials in extractor app web interface
- Check credential ID/name configuration
- Verify credentials are marked as active

### API Key Issues

**Problem**: API calls fail with authentication errors
**Solutions**:
- Check credentials in extractor app are valid
- Verify API keys are not expired
- Test API keys manually
- Check fallback environment variables

### Performance Issues

**Problem**: Slow credential fetching
**Solutions**:
- Increase `CREDENTIALS_CACHE_TIMEOUT`
- Check network latency to extractor app
- Consider running extractor app locally

## Security Considerations

1. **Secure Storage**: Credentials are encrypted in the extractor app database
2. **Network Security**: Use HTTPS for production deployments
3. **Access Control**: Limit extractor app access to authorized users
4. **Credential Rotation**: Regularly update API keys
5. **Fallback Security**: Secure local environment variables appropriately

## Migration from Local Environment

To migrate from local environment variables:

1. **Export existing credentials** to extractor app:
   ```bash
   cd nch_extractor
   python manage.py import_ai_env --name "Migrated Credentials"
   ```

2. **Configure AI hedge fund** to use extractor:
   ```bash
   cd ai-hedge-fund
   python setup_extractor_integration.py
   ```

3. **Test the integration**:
   ```bash
   python test_extractor_integration.py
   ```

4. **Remove local credentials** (optional):
   Remove API keys from local `.env` file, keeping only extractor configuration.

## Support

For issues with the integration:

1. **Check logs** for detailed error messages
2. **Run test script** to diagnose issues
3. **Verify configuration** in both apps
4. **Check network connectivity** between apps
5. **Review extractor app logs** for server-side issues

## Examples

### Basic Usage

```python
# The integration is transparent - existing code works unchanged
from src.llm.models import get_model, ModelProvider

# This will automatically use credentials from extractor app
model = get_model("gpt-4o", ModelProvider.OPENAI)
```

### Manual Credential Access

```python
from src.utils.credentials_client import get_credentials_client

client = get_credentials_client()

# Check what providers are available
credentials = client.get_credentials()
providers = [key.replace('_API_KEY', '').lower() 
            for key in credentials.keys() 
            if key.endswith('_API_KEY')]

print(f"Available providers: {providers}")
```

### Error Handling

```python
from src.utils.credentials_client import get_api_key

try:
    api_key = get_api_key('openai')
    if not api_key:
        print("OpenAI API key not configured")
    else:
        # Use the API key
        pass
except Exception as e:
    print(f"Error fetching credentials: {e}")
    # Handle error or use fallback
```
