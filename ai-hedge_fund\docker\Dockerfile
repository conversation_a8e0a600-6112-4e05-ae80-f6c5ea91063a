FROM python:3.11-slim

WORKDIR /app

# Set PYTHONPATH to include the app directory
ENV PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies directly with pip (let pip resolve versions)
RUN pip install --no-cache-dir \
    fastapi[standard] \
    fastapi-cli \
    pydantic \
    httpx \
    sqlalchemy \
    alembic \
    psycopg2-binary \
    uvicorn[standard] \
    langchain==0.3.0 \
    langchain-anthropic==0.3.5 \
    langchain-groq==0.2.3 \
    langchain-openai \
    langchain-deepseek \
    langchain-ollama \
    langgraph \
    pandas \
    numpy \
    python-dotenv \
    matplotlib \
    tabulate \
    colorama \
    questionary \
    rich \
    langchain-google-genai

# Copy rest of the source code
COPY . /app/

# Default command (will be overridden by <PERSON><PERSON> Compose)
CMD ["python", "src/main.py"] 
