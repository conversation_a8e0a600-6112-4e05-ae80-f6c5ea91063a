import os
from django.core.management.base import BaseCommand
from ai_hedge_fund_provider.models import AIHedgeFundCreds


class Command(BaseCommand):
    help = 'Import AI hedge fund credentials from environment variables'

    def add_arguments(self, parser):
        parser.add_argument(
            '--name',
            type=str,
            default='Environment Credentials',
            help='Name for the credential set'
        )
        parser.add_argument(
            '--description',
            type=str,
            default='Imported from environment variables',
            help='Description for the credential set'
        )
        parser.add_argument(
            '--update',
            action='store_true',
            help='Update existing credentials with the same name'
        )

    def handle(self, *args, **options):
        name = options['name']
        description = options['description']
        update = options['update']

        # Check if credentials with this name already exist
        existing_creds = AIHedgeFundCreds.objects.filter(name=name).first()
        
        if existing_creds and not update:
            self.stdout.write(
                self.style.WARNING(
                    f'Credentials with name "{name}" already exist. Use --update to update them.'
                )
            )
            return

        # Collect environment variables
        env_vars = {
            'anthropic_api_key': os.getenv('ANTHROPIC_API_KEY'),
            'deepseek_api_key': os.getenv('DEEPSEEK_API_KEY'),
            'groq_api_key': os.getenv('GROQ_API_KEY'),
            'google_api_key': os.getenv('GOOGLE_API_KEY'),
            'openai_api_key': os.getenv('OPENAI_API_KEY'),
            'financial_datasets_api_key': os.getenv('FINANCIAL_DATASETS_API_KEY'),
            'openai_api_base': os.getenv('OPENAI_API_BASE'),
        }

        # Filter out None values
        env_vars = {k: v for k, v in env_vars.items() if v}

        if not env_vars:
            self.stdout.write(
                self.style.ERROR(
                    'No AI hedge fund environment variables found. '
                    'Please set at least one of: ANTHROPIC_API_KEY, DEEPSEEK_API_KEY, '
                    'GROQ_API_KEY, GOOGLE_API_KEY, OPENAI_API_KEY, FINANCIAL_DATASETS_API_KEY'
                )
            )
            return

        # Create or update credentials
        if existing_creds:
            for key, value in env_vars.items():
                setattr(existing_creds, key, value)
            existing_creds.description = description
            existing_creds.save()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully updated credentials "{name}" with {len(env_vars)} API keys'
                )
            )
        else:
            creds = AIHedgeFundCreds.objects.create(
                name=name,
                description=description,
                is_active=True,
                **env_vars
            )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created credentials "{name}" with {len(env_vars)} API keys'
                )
            )

        # Show which providers are available
        if existing_creds:
            providers = existing_creds.get_available_providers()
        else:
            providers = creds.get_available_providers()
            
        self.stdout.write(
            self.style.SUCCESS(
                f'Available providers: {", ".join(providers)}'
            )
        )
