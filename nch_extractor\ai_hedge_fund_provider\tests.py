from django.test import TestCase
from django.contrib.auth.models import User
from rest_framework.test import APIClient
from rest_framework import status
from .models import AIHedgeFundCreds


class AIHedgeFundCredsTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        self.creds_data = {
            'name': 'Test Credentials',
            'description': 'Test description',
            'is_active': True,
            'openai_api_key': 'sk-test123',
            'anthropic_api_key': 'sk-ant-test123'
        }

    def test_create_credentials(self):
        """Test creating AI hedge fund credentials"""
        response = self.client.post(
            '/ai-hedge-fund/ai-hedge-fund-creds/',
            self.creds_data,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check that credentials were created
        creds = AIHedgeFundCreds.objects.get(name='Test Credentials')
        self.assertEqual(creds.name, 'Test Credentials')
        self.assertTrue(creds.is_active)

    def test_list_credentials(self):
        """Test listing AI hedge fund credentials"""
        # Create test credentials
        AIHedgeFundCreds.objects.create(**self.creds_data)
        
        response = self.client.get('/ai-hedge-fund/ai-hedge-fund-creds/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('ai_hedge_fund_credentials', data)
        self.assertEqual(len(data['ai_hedge_fund_credentials']), 1)

    def test_credentials_masking(self):
        """Test that API keys are masked in responses"""
        creds = AIHedgeFundCreds.objects.create(**self.creds_data)
        
        response = self.client.get('/ai-hedge-fund/ai-hedge-fund-creds/')
        data = response.json()
        
        cred_data = data['ai_hedge_fund_credentials'][0]
        # API keys should be masked
        self.assertNotEqual(cred_data['openai_api_key'], 'sk-test123')
        self.assertTrue(cred_data['openai_api_key'].startswith('sk-t'))
        self.assertTrue(cred_data['openai_api_key'].endswith('23'))

    def test_available_providers(self):
        """Test that available providers are correctly identified"""
        creds = AIHedgeFundCreds.objects.create(**self.creds_data)
        
        providers = creds.get_available_providers()
        self.assertIn('OpenAI', providers)
        self.assertIn('Anthropic', providers)
        self.assertEqual(len(providers), 2)

    def test_export_functionality(self):
        """Test exporting credentials as .env file"""
        creds = AIHedgeFundCreds.objects.create(**self.creds_data)
        
        response = self.client.get(f'/ai-hedge-fund/ai-hedge-fund-creds/export/{creds.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response['Content-Type'], 'text/plain')
        
        # Check that the response contains environment variables
        content = response.content.decode('utf-8')
        self.assertIn('OPENAI_API_KEY=sk-test123', content)
        self.assertIn('ANTHROPIC_API_KEY=sk-ant-test123', content)

    def test_update_credentials(self):
        """Test updating existing credentials"""
        creds = AIHedgeFundCreds.objects.create(**self.creds_data)
        
        update_data = {
            'id': creds.id,
            'name': 'Updated Credentials',
            'groq_api_key': 'gsk-test123'
        }
        
        response = self.client.put(
            '/ai-hedge-fund/ai-hedge-fund-creds/',
            update_data,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that credentials were updated
        creds.refresh_from_db()
        self.assertEqual(creds.name, 'Updated Credentials')
        self.assertEqual(creds.groq_api_key, 'gsk-test123')

    def test_delete_credentials(self):
        """Test deleting credentials"""
        creds = AIHedgeFundCreds.objects.create(**self.creds_data)
        
        response = self.client.delete(
            '/ai-hedge-fund/ai-hedge-fund-creds/',
            {'id': creds.id},
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that credentials were deleted
        self.assertFalse(AIHedgeFundCreds.objects.filter(id=creds.id).exists())

    def test_validation_requires_at_least_one_api_key(self):
        """Test that at least one API key is required"""
        invalid_data = {
            'name': 'Invalid Credentials',
            'description': 'No API keys provided'
        }
        
        response = self.client.post(
            '/ai-hedge-fund/ai-hedge-fund-creds/',
            invalid_data,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
