import pytest
from datetime import date, datetime
from decimal import Decimal
from django.test import TestCase
from django.utils import timezone
from unittest.mock import patch, MagicMock

from port.models import (
    Bnr, Currency, Deposits, Journal, Operation, Instrument,
    Ubo, Custodian, Account, Partner
)
from port.services.provider.deposits_service import DepositsService


class DepositsServiceTestCase(TestCase):
    """Test cases for DepositsService"""
    
    def setUp(self):
        """Set up test data"""
        # Create test currencies
        self.ron_currency = Currency.objects.create(
            currency_code='RON',
            currency_name='Romanian Leu'
        )
        self.usd_currency = Currency.objects.create(
            currency_code='USD',
            currency_name='US Dollar'
        )
        
        # Create test UBO
        self.ubo = Ubo.objects.create(
            ubo_code='DD',
            ubo_name='Test UBO'
        )
        
        # Create test custodian
        self.custodian = Custodian.objects.create(
            custodian_code='TEST_BANK',
            custodian_name='Test Bank'
        )
        
        # Create test BNR rates
        Bnr.objects.create(
            date=date(2024, 1, 1),
            currency_code=self.usd_currency,
            value_exact=Decimal('4.5000')
        )
        Bnr.objects.create(
            date=date(2024, 2, 1),
            currency_code=self.usd_currency,
            value_exact=Decimal('4.6000')
        )
        
        # Create test instrument
        self.instrument = Instrument.objects.create(
            symbol='TEST_DEP_001',
            instrument_name='Test Deposit',
            currency=self.usd_currency,
            custodian=self.custodian,
            instrument_type='DEPOSIT'
        )
        
        # Create test deposit
        self.deposits = Deposits.objects.create(
            deposit=self.instrument,
            principal=Decimal('10000.00'),
            interest_rate=Decimal('5.00'),
            convention=365,
            start=date(2024, 1, 15),
            maturity=date(2024, 3, 15),
            interest_amount=Decimal('250.00'),
            new_deposit=True,
            liquidated=False
        )
    
    def test_service_initialization(self):
        """Test service initialization"""
        service = DepositsService()
        self.assertIsNotNone(service.ubo)
        self.assertEqual(service.ubo.ubo_code, 'DD')
    
    def test_get_deposits_queryset(self):
        """Test getting deposits queryset"""
        service = DepositsService()
        
        # Test getting all deposits
        queryset = service._get_deposits_queryset()
        self.assertEqual(queryset.count(), 1)
        
        # Test filtering by symbol
        queryset = service._get_deposits_queryset('TEST_DEP_001')
        self.assertEqual(queryset.count(), 1)
        
        # Test filtering by non-existent symbol
        queryset = service._get_deposits_queryset('NON_EXISTENT')
        self.assertEqual(queryset.count(), 0)
    
    def test_extract_deposit_info(self):
        """Test extracting deposit information"""
        service = DepositsService()
        deposit_info = service._extract_deposit_info(self.deposits)
        
        self.assertEqual(deposit_info['principal'], 10000.0)
        self.assertEqual(deposit_info['interest_rate'], 5.0)
        self.assertEqual(deposit_info['convention'], 365)
        self.assertEqual(deposit_info['currency'], 'USD')
        self.assertEqual(deposit_info['custodian'], 'TEST_BANK')
        self.assertEqual(deposit_info['deposit_id'], 'TEST_DEP_001')
        self.assertTrue(deposit_info['prima_constituire'])
        self.assertFalse(deposit_info['ultima_lichidare'])
    
    def test_get_currency_rates(self):
        """Test getting currency rates"""
        service = DepositsService()
        bnr_rates = service._get_bnr_rates()
        
        # Test USD rates
        usd_rates = service._get_currency_rates(bnr_rates, 'USD')
        self.assertEqual(len(usd_rates), 2)
        self.assertEqual(usd_rates[date(2024, 1, 1)], 4.5)
        self.assertEqual(usd_rates[date(2024, 2, 1)], 4.6)
        
        # Test RON rates (should always be 1.0)
        ron_rates = service._get_currency_rates(bnr_rates, 'RON')
        self.assertTrue(all(rate == 1.0 for rate in ron_rates.values()))
    
    def test_get_bnr_rate_before_date(self):
        """Test getting BNR rate before a specific date"""
        service = DepositsService()
        
        currency_rates = {
            date(2024, 1, 1): 4.5,
            date(2024, 2, 1): 4.6
        }
        
        # Test exact date
        rate = service._get_bnr_rate_before_date(currency_rates, date(2024, 1, 1))
        self.assertEqual(rate, 4.5)
        
        # Test date between rates
        rate = service._get_bnr_rate_before_date(currency_rates, date(2024, 1, 15))
        self.assertEqual(rate, 4.5)
        
        # Test date after all rates
        rate = service._get_bnr_rate_before_date(currency_rates, date(2024, 3, 1))
        self.assertEqual(rate, 4.6)
        
        # Test date before all rates
        rate = service._get_bnr_rate_before_date(currency_rates, date(2023, 12, 1))
        self.assertEqual(rate, 4.5)  # Should return first available rate
    
    def test_generate_end_of_months(self):
        """Test generating end of month dates"""
        service = DepositsService()
        
        start_date = date(2024, 1, 15)
        end_date = date(2024, 3, 15)
        
        end_of_months = service._generate_end_of_months(start_date, end_date)
        
        expected_dates = [
            date(2024, 1, 31),
            date(2024, 2, 29),  # 2024 is a leap year
            date(2024, 3, 31)
        ]
        
        self.assertEqual(end_of_months, expected_dates)
    
    def test_get_deposit_summary(self):
        """Test getting deposit summary"""
        service = DepositsService()
        summary = service.get_deposit_summary()
        
        self.assertEqual(summary['total_deposits'], 1)
        self.assertEqual(len(summary['deposits']), 1)
        
        deposit_summary = summary['deposits'][0]
        self.assertEqual(deposit_summary['symbol'], 'TEST_DEP_001')
        self.assertEqual(deposit_summary['custodian'], 'TEST_BANK')
        self.assertEqual(deposit_summary['currency'], 'USD')
        self.assertEqual(deposit_summary['principal'], 10000.0)
        self.assertEqual(deposit_summary['interest_rate'], 5.0)
        self.assertTrue(deposit_summary['is_new'])
        self.assertFalse(deposit_summary['is_liquidated'])
    
    @patch('port.services.provider.deposits_service.timezone')
    def test_calculate_accruals_for_deposit(self, mock_timezone):
        """Test calculating accruals for a deposit"""
        # Mock current date
        mock_timezone.now.return_value.date.return_value = date(2024, 2, 15)
        
        service = DepositsService()
        deposit_info = service._extract_deposit_info(self.deposits)
        bnr_rates = service._get_bnr_rates()
        
        accrual_entries = service._calculate_accruals_for_deposit(deposit_info, bnr_rates)
        
        # Should have at least constitution entry
        self.assertGreater(len(accrual_entries), 0)
        
        # First entry should be constitution
        first_entry = accrual_entries[0]
        self.assertEqual(first_entry['operation'], 'CONSTITUIRE_DEP_VALUTA')
        self.assertEqual(first_entry['value'], -10000.0)
        self.assertEqual(first_entry['currency'], 'USD')
        self.assertEqual(first_entry['quantity'], 10000.0)
        
        # Check that all entries have required fields
        for entry in accrual_entries:
            self.assertIn('date', entry)
            self.assertIn('operation', entry)
            self.assertIn('value', entry)
            self.assertIn('currency', entry)
            self.assertIn('bnr', entry)
            self.assertIn('value_ron', entry)
            self.assertIn('transactionid', entry)
            self.assertIn('custodian', entry)
            self.assertIn('ubo', entry)
    
    def test_create_journal_entries_integration(self):
        """Test creating journal entries (integration test)"""
        service = DepositsService()
        
        # Create sample accrual entries
        accrual_entries = [
            {
                'date': date(2024, 1, 15),
                'operation': 'CONSTITUIRE_DEP_VALUTA',
                'value': -10000.0,
                'currency': 'USD',
                'bnr': 4.5,
                'value_ron': -45000.0,
                'quantity': 10000.0,
                'transactionid': 'TEST_DEP_001 CONSTITUIRE 2024-01-15',
                'custodian': 'TEST_BANK',
                'ubo': 'DD',
                'partner': 'TEST_BANK',
                'account': 'TEST_BANK_USD',
                'instrument': 'TEST_BANK_TEST_DEP_001',
                'details': 'TEST_DEP_001 CONSTITUIRE_DEP_VALUTA',
                'idx': 'ADAUGA',
                'storno': False
            }
        ]
        
        # Create journal entries
        created_count = service._create_journal_entries(accrual_entries, self.deposits)
        
        self.assertEqual(created_count, 1)
        
        # Verify journal entry was created
        journal = Journal.objects.get(transactionid='TEST_DEP_001 CONSTITUIRE 2024-01-15')
        self.assertEqual(journal.value, Decimal('-10000.00'))
        self.assertEqual(journal.currency.currency_code, 'USD')
        self.assertEqual(journal.bnr, Decimal('4.5'))
        self.assertEqual(journal.value_ron, Decimal('-45000.00'))
        self.assertEqual(journal.quantity, Decimal('10000.00'))
        self.assertEqual(journal.ubo.ubo_code, 'DD')
        self.assertEqual(journal.custodian.custodian_code, 'TEST_BANK')
        self.assertEqual(journal.idx, 'ADAUGA')
        self.assertFalse(journal.storno)
    
    def test_update_or_create_journal_entry_behavior(self):
        """Test that journal entries are created or updated using update_or_create"""
        service = DepositsService()

        # Create sample accrual entry
        accrual_entries = [
            {
                'date': date(2024, 1, 15),
                'operation': 'CONSTITUIRE_DEP_VALUTA',
                'value': -10000.0,
                'currency': 'USD',
                'bnr': 4.5,
                'value_ron': -45000.0,
                'quantity': 10000.0,
                'transactionid': 'TEST_DEP_001 CONSTITUIRE 2024-01-15',
                'custodian': 'TEST_BANK',
                'ubo': 'DD',
                'partner': 'TEST_BANK',
                'account': 'TEST_BANK_USD',
                'instrument': 'TEST_BANK_TEST_DEP_001',
                'details': 'TEST_DEP_001 CONSTITUIRE_DEP_VALUTA',
                'idx': 'ADAUGA',
                'storno': False
            }
        ]

        # Create journal entries first time
        processed_count_1 = service._create_journal_entries(accrual_entries, self.deposits)
        self.assertEqual(processed_count_1, 1)

        # Verify journal entry was created
        journal = Journal.objects.get(transactionid='TEST_DEP_001 CONSTITUIRE 2024-01-15')
        self.assertEqual(journal.value, Decimal('-10000.00'))

        # Modify the entry and process again (should update)
        accrual_entries[0]['value'] = -15000.0
        accrual_entries[0]['value_ron'] = -67500.0

        processed_count_2 = service._create_journal_entries(accrual_entries, self.deposits)
        self.assertEqual(processed_count_2, 1)  # Should process (update) the entry

        # Verify the journal entry was updated
        journal.refresh_from_db()
        self.assertEqual(journal.value, Decimal('-15000.00'))
        self.assertEqual(journal.value_ron, Decimal('-67500.00'))

        # Verify only one journal entry exists
        journal_count = Journal.objects.filter(
            transactionid='TEST_DEP_001 CONSTITUIRE 2024-01-15'
        ).count()
        self.assertEqual(journal_count, 1)
