from django.contrib import admin
from .models import AIHedgeFundCreds


@admin.register(AIHedgeFundCreds)
class AIHedgeFundCredsAdmin(admin.ModelAdmin):
    list_display = ['name', 'is_active', 'get_providers_display', 'has_financial_data', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at', 'get_available_providers']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'is_active')
        }),
        ('AI Provider API Keys', {
            'fields': (
                'anthropic_api_key', 'deepseek_api_key', 'groq_api_key',
                'google_api_key', 'openai_api_key', 'openai_api_base'
            ),
            'description': 'API keys for various AI providers. Keys are encrypted in the database.'
        }),
        ('Financial Data', {
            'fields': ('financial_datasets_api_key',),
            'description': 'API key for financial data access.'
        }),
        ('Metadata', {
            'fields': ('get_available_providers', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def get_providers_display(self, obj):
        """Display available providers in admin list"""
        providers = obj.get_available_providers()
        return ', '.join(providers) if providers else 'None'
    get_providers_display.short_description = 'Available Providers'
    
    def has_financial_data(self, obj):
        """Display if financial data access is available"""
        return obj.has_financial_data_access()
    has_financial_data.boolean = True
    has_financial_data.short_description = 'Financial Data'
