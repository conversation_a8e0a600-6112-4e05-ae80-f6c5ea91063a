# AI Hedge Fund Provider

This Django app provides secure credential management for AI hedge fund applications, supporting multiple AI providers and financial data services.

## Features

- **Secure Storage**: All API keys are encrypted using Django's encrypted model fields
- **Multiple Providers**: Support for OpenAI, Anthropic, Groq, Google, DeepSeek, and Financial Datasets
- **Web Interface**: Easy-to-use web interface for managing credentials
- **Export Functionality**: Export credentials as .env files for easy deployment
- **API Integration**: RESTful API for programmatic access

## Supported API Providers

### AI Providers
- **OpenAI**: GPT models (gpt-4o, gpt-4, etc.)
- **Anthropic**: Claude models (claude-3-5-sonnet, claude-3-opus, etc.)
- **Groq**: Llama and other models
- **Google**: Gemini models
- **DeepSeek**: DeepSeek models

### Financial Data
- **Financial Datasets**: Market data and financial information

## Installation

1. Add `ai_hedge_fund_provider` to your `INSTALLED_APPS` in Django settings
2. Run migrations: `python manage.py makemigrations ai_hedge_fund_provider`
3. Apply migrations: `python manage.py migrate`
4. Include URLs in your main `urls.py`

## Usage

### Web Interface

Navigate to the credentials management page and:
1. Click "Add Credentials"
2. Select "AI Hedge Fund" as the credential type
3. Fill in the required information and API keys
4. Save the credentials

### Export .env File

1. Go to the credentials management page
2. Find your AI hedge fund credentials
3. Click the "Export .env" button
4. Download the generated .env file

### API Endpoints

- `GET /ai-hedge-fund/ai-hedge-fund-creds/` - List all credentials
- `POST /ai-hedge-fund/ai-hedge-fund-creds/` - Create new credentials
- `PUT /ai-hedge-fund/ai-hedge-fund-creds/` - Update existing credentials
- `DELETE /ai-hedge-fund/ai-hedge-fund-creds/` - Delete credentials
- `GET /ai-hedge-fund/ai-hedge-fund-creds/export/{id}/` - Export as .env file

## Environment Variables

The following environment variables are supported:

```bash
# AI Provider API Keys
ANTHROPIC_API_KEY=your-anthropic-api-key
OPENAI_API_KEY=your-openai-api-key
GROQ_API_KEY=your-groq-api-key
GOOGLE_API_KEY=your-google-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key

# Financial Data
FINANCIAL_DATASETS_API_KEY=your-financial-datasets-api-key

# Optional
OPENAI_API_BASE=your-custom-openai-base-url
```

## Security

- All API keys are encrypted at rest using Django's encrypted model fields
- Sensitive data is masked in API responses
- Only authenticated users can access credentials
- Export functionality requires authentication

## Models

### AIHedgeFundCreds

Main model for storing AI hedge fund credentials:

- `name`: Friendly name for the credential set
- `description`: Optional description
- `is_active`: Whether the credentials are active
- `anthropic_api_key`: Encrypted Anthropic API key
- `openai_api_key`: Encrypted OpenAI API key
- `groq_api_key`: Encrypted Groq API key
- `google_api_key`: Encrypted Google API key
- `deepseek_api_key`: Encrypted DeepSeek API key
- `financial_datasets_api_key`: Encrypted Financial Datasets API key
- `openai_api_base`: Optional custom OpenAI base URL
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp
