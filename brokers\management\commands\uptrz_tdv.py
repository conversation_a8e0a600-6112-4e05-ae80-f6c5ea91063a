
""" Export TRADEVILLE transactions"""
from django.core.management.base import BaseCommand
from django.conf import settings 
from django.db.models import Max # Generates a "SELECT MAX..." query
from port.models import Inst<PERSON>ent, Ubo, Partner, Operation, Journal, Custodian, Account, Lock
import glob
import os
import pandas as pd
from datetime import datetime

class Command(BaseCommand):

    def handle(self, *args, **options):

        # pd.set_option('display.max_columns', None)

        broker = 'tdv'
        UBO = ['DD', 'AS']
        CURRENCIES = ['EUR', 'USD', 'RON', 'GBP', 'CAD', 'SEK',]

        today = str(datetime.today())[:10]
        root = settings.FILE_ROOT + "{broker}/".format(broker=broker)

        lock_date = Lock.objects.aggregate(Max('lock_date'))['lock_date__max']
        print('lock_date', lock_date)
        
        # Import relevant tables
        tabs = [
            'activitate', 
            'portof',
            ]

        tab = {}
        
        for t in tabs:
            list_of_files = glob.glob(root + t + '*') 
            latest_file = max(list_of_files, key=os.path.getctime)
            df = pd.read_csv(latest_file, na_filter=False).dropna(axis=1, how='all')
            tab[t] = df 

        # Import portfolio
        cols = {
            'valuta': 'currency',
            'simbol': 'symbol',
            'isin': 'isin',
            'nume': 'name',
            'firma': 'ubo',
            'sold': 'quantity',
            'costm': 'unit_cost',
            'ppiata': 'value',
            'valuta': 'currency',
            }
        portfolio =  tab['portof'].rename(columns=cols)[cols.values()]
        portfolio['cost'] = portfolio['unit_cost'] * portfolio['quantity']
        portfolio['value'] = portfolio['value'] * portfolio['quantity']
        portfolio['value_gross'] = portfolio['value']

        portfolio.loc[portfolio['symbol'].isin(CURRENCIES), 'cost'] = 1
        # print(portfolio)


        # Import transactions
        cols = {
            'id': 'transactionid',
            'op': 'operation',
            'simbol': 'symbol',
            'data': 'date',
            'firma': 'ubo',
            'suma': 'valuex',
            'cant': 'quantity',
            'descr': 'details',
            'valuta': 'currency',
            'costm': 'unit_cost',

            'pret': 'price', 
            'dirty': 'dirty', 
            'comis': 'comis', # to split
            'txcnlei': 'txcnlei', # to split
            'profit': 'profit', # to split
            }

        df =  tab['activitate'].rename(columns=cols)[cols.values()].copy()
        print(df['symbol'].unique())
        df['date'] = df['date'].str[:10]
        col_float = ['price', 'dirty', 'valuex', 'quantity', 'comis', 'txcnlei', 'profit']
        df[col_float] = df[col_float].replace('', '0').astype(float)
        
        # Operation sign
        out_cash = ['cump', 'out',]
        df.loc[df['operation'].isin(out_cash), 'price'] = -df['price']
        df.loc[df['symbol'].isin(CURRENCIES), 'price'] = 1
        df.loc[df['operation'].isin(out_cash), 'dirty'] = -df['dirty']
        df.loc[df['operation'].isin(['out']), 'quantity'] = -df['quantity']
        df['value'] = df['price'] * df['quantity']
        df['value_gross'] = df['value']

        # Separate interest
        col = 'interest'
        df[col] = 0.0
        df.loc[df['dirty']!=0.0, col] = df['quantity'] * (df['dirty'] - df['price'])
        df.loc[df['dirty']!=0.0, 'value'] = df['quantity'] * df['price'] 

        journal = df.copy()

        dfx = df[df[col]!=0].copy()
        dfx['value'] = dfx[col]  
        dfx['quantity'] = 0.0
        dfx['unit_cost'] = 0.0
        dfx.loc[dfx['operation'].isin(['cump']), 'operation'] = 'BOND_INTEREST_PAID_BROKER'
        dfx.loc[dfx['operation'].isin(['vanz']), 'operation'] = 'BOND_INTEREST_RECEIVED_BROKER'

        journal = pd.concat([journal, dfx], ignore_index=True)

        # Separate commission
        col = 'comis'
        dfx = df[df[col]!=0].copy()
        dfx['value'] = -dfx[col]
        dfx['quantity'] = 0.0
        dfx['unit_cost'] = 0.0
        dfx['temp'] = dfx['operation']
        dfx['operation'] = 'COMIS_BROKER_VALUTA'
        COM_BUY = ['cump',]
        COM_SELL = ['vanz',]
        dfx.loc[dfx['temp'].isin(COM_BUY), 'operation'] = 'COMIS_BROKER_VALUTA'
        dfx.loc[dfx['temp'].isin(COM_SELL), 'operation'] = 'COMIS_BROKER_VALUTA'

        journal = pd.concat([journal, dfx], ignore_index=True)
        
        
        # Separate TX CNVM
        col = 'txcnlei'
        dfx = df[df[col]!=0.0].copy()
        dfx['value'] = -dfx[col]
        dfx['operation'] = 'COMIS_BROKER_VALUTA'
        dfx['quantity'] = 0.0
        dfx['unit_cost'] = 0.0

        journal['value'] = journal['value'] - journal[col]
        journal = pd.concat([journal, dfx], ignore_index=True)

        journal.drop(columns=[
            'valuex', 'interest', 'dirty', 'price', 'temp', 'txcnlei', 'comis'], inplace=True)
        journal[['value', 'quantity', 'profit']] = journal[['value', 'quantity', 'profit']]
        journal.loc[journal['symbol'].isin(CURRENCIES), 'quantity'] = 0

        # Rename
        journal.loc[journal['operation']=='cump', 'operation'] = 'BUY_BOND_BROKER'
        journal.loc[journal['operation']=='vanz', 'operation'] = 'SELL_BOND_BROKER'
        journal.loc[journal['operation']=='div', 'operation'] = 'BOND_INTEREST_RECEIVED_BROKER'

        # Reconciliere cantitati
        df_now = portfolio[['symbol', 'quantity', 'ubo']].rename(columns={'quantity': 'quantity_real'})
        df_now = df_now[~df_now['symbol'].isin(CURRENCIES)]
        df_recon = journal.pivot_table(index=['symbol', 'ubo'],values=['quantity',], aggfunc='sum').reset_index()
        print(df_recon)
        print(df_now)
        df_recon = df_recon.merge(df_now, how='outer', on=['symbol', 'ubo'], validate='1:1').fillna(0.0)
        df_recon['diff'] = df_recon['quantity_real'] - df_recon['quantity']
        if (abs(min(df_recon['diff']))>=0.01)| (abs(max(df_recon['diff']))>=0.01 ):
            print('Eroare reconciliere cantitati')
            print(df_recon)
        else:
            print('Reconciliere cantitati ok')

        # Reconciliere sume
        df_now = portfolio[portfolio['symbol'].isin(CURRENCIES)][['symbol', 'value', 'ubo']].rename(columns={'value': 'value_real', 'symbol': 'currency'})
        # sum by currency and ubo
        df_now = df_now.pivot_table(index=['currency', 'ubo'], values=['value_real'], aggfunc='sum').reset_index()
        
        df_recon = journal.pivot_table(index=['currency', 'ubo'],values=['value',], aggfunc='sum').reset_index()
        df_recon = df_recon[df_recon['value']!=0.0]
        print(df_recon)
        print(df_now)

        df_recon = df_recon.merge(df_now, how='outer', on=['currency', 'ubo'], validate='1:1').fillna(0.0)
        df_recon['diff'] = df_recon['value'] - df_recon['value_real']
        if (abs(min(df_recon['diff']))>=0.02)| (abs(max(df_recon['diff']))>=0.02 ):
            print('Eroare reconciliere sume')
            print(df_recon)
        else:
            print('Reconciliere sume ok')

        journal = journal.sort_values(by=['date', 'operation'])
        journal['value'] = journal['value'].round(2)

        # Attach custodian, partner counterparty
        journal['custodian'] = broker.upper()
        journal['partner'] = journal['custodian']

        # Attach account
        journal['account'] = 'TDV_' + journal['currency']
        journal.loc[
            (journal['currency']=='EUR') 
            & (~journal['details'].str.contains('.')), 'account'
            ] = 'TDV_RE'
        
        # Recodificare
        journal['details'] = journal['details'] .apply(lambda x: ' '.join(x.upper().split()))

        # Recodificare transferuri RE-EUR
        re_eur = ['TRANSFER DIN D6DN39-RE IN D6DN39.UE', 'TRANSFER DIN RE IN UE']
        journal.loc[
            (journal['operation'].isin(['in', 'out']))
            & (journal['details'].isin(re_eur))
            , 'operation'] = 'TRANSFER_INTERN_BROKER'
        
        # Recodificare alimentari 
        journal.loc[
            (journal['operation']=='in')
            & (journal['details'].str.startswith('DEPUNERE'))
            , 'partner'] = 'NEDEFINIT'
        journal.loc[
            (journal['operation']=='in')
            & (journal['details'].str.startswith('DEPUNERE'))
            , 'operation'] = 'VIR_INT_IN_BROKER_VALUTA'
        
        # Recodificare retrageri
        journal.loc[
            (journal['operation']=='out')
            & (journal['details'].str.startswith('RETRAGERE'))
            , 'partner'] = 'NEDEFINIT'
        journal.loc[
            (journal['operation']=='out')
            & (journal['details'].str.startswith('RETRAGERE'))
            , 'operation'] = 'VIR_INT_OUT_BROKER_VALUTA'

        # Recodificare schimburi valutare
        fx_in = ['TRANSFER DIN D6DN39.UE IN D6DN39.US']
        journal.loc[
            (journal['operation'].isin(['in', ]))
            & (journal['details'].isin(fx_in))
            , 'operation'] = 'FX_IN'
        
        fx_out = 'USD IN US CURS'
        journal.loc[
            (journal['operation'].isin(['out', ]))
            & (journal['details'].str.contains(fx_out))
            , 'operation'] = 'FX_OUT'
        
        
        # Filtrare data
        # if 'date' in journal.columns:
        #     journal = journal[journal['date']>str(lock_date)]

        journal.sort_values(by=['transactionid'], inplace=True, ignore_index=True)

        # """ COST MEDIU """
        # # Pregatire Calcul cost mediu ponderat
        # TRADES = ['BUY_BOND', 'SELL_BOND']
        # all_x = journal[~(journal['operation'].isin(TRADES))].copy()
        # trades = journal[journal['operation'].isin(TRADES)].copy()

        # # Calcul cost mediu ponderat
        # df = pd.DataFrame()
        # for symbol in trades['symbol'].unique():
        #     dx = trades[trades['symbol']==symbol].reset_index(drop=True).copy()
        #     dx[['value_at_cost','unit_cost']] = [0.0, 0.0 ]
        #     dx[['quantity', 'value']] = dx[['quantity', 'value']].astype(float)
            
        #     latest_cost = 0
        #     cumulated_value = 0
        #     cumulated_quantity = 0

        #     for index, row in dx.iterrows():
        #         if row['quantity'] >= 0:
        #             dx.loc[index, 'value_at_cost'] = -row['value']
        #             cumulated_value += -row['value']
        #             cumulated_quantity += row['quantity']
        #             latest_cost = cumulated_value / cumulated_quantity
        #             dx.loc[index, 'unit_cost'] = latest_cost
        #         else:
        #             cost = row['quantity'] * latest_cost
        #             dx.loc[index, 'value_at_cost'] = cost
        #             cumulated_quantity += row['quantity']
        #             cumulated_value += cost

        #             if cumulated_quantity > 0:
        #                 latest_cost = cumulated_value / cumulated_quantity
        #             else:
        #                 latest_cost = 0
        #             dx.loc[index, 'unit_cost'] = latest_cost
                    
        #     dx['profit'] = dx['value'] + dx['value_at_cost']
        #     df = pd.concat([df, dx], ignore_index=True)

        # df = df.drop(columns=['value_at_cost'])
        
        # journal = pd.concat([df, all_x], ignore_index=True)
        # journal['unit_cost'] = journal['unit_cost'].fillna(0)
        # journal['profit'] = journal['profit'].fillna(0)

        # # Add profits
        # trades_profit = journal[journal['profit']!=0].copy()
        # if len(trades_profit)>0:
        #     trades_profit['operation'] = 'PROFIT'
        #     trades_profit.loc[trades_profit['profit']<0, 'type'] = 'PIERDERE'
        #     trades_profit['value'] = trades_profit['profit']
        #     trades_profit['quantity'] = 0
        #     trades_profit['details'] = 'profit'

            # journal = pd.concat([journal, trades_profit, ], ignore_index=True)

        journal['profit'] = 0.0

        today = str(datetime.today())[:10]
        save_file = settings.FILE_ROOT + f"reports/journal_tdv_{today}.xlsx"
        journal.to_excel(save_file, index=False)

        # Missing operations
        vals = journal['operation'].unique()
        all_vals = Operation.objects.values_list('operation_code', flat=True)
        print('Missing operations:', set(vals) - set(all_vals))

        # Missing instruments
        vals = journal['symbol'].unique()
        print('Instruments:', vals)
        all_vals = Instrument.objects.values_list('symbol', flat=True)
        print('Missing instruments:', set(vals) - set(all_vals))

        cols_not_null = ['value', 'quantity', 'profit',  'value_gross']
        journal[cols_not_null] = journal[cols_not_null].fillna(0).astype(float)


        # # Prepare export to database        
        # model_instances = [
        #     Journal(
        #         # currency_code = Currency.objects.get(currency_code = row['currency']),
        #         ubo = Ubo.objects.get(ubo_code = row['ubo']),
        #         custodian = Custodian.objects.get(custodian_code = row['custodian']),
        #         account = Account.objects.get(account_code = row['account']),
        #         operation = Operation.objects.get(operation_code = row['operation']),
        #         partner = Partner.objects.get(partner_code = row['partner']),
        #         instrument = Instrument.objects.get(symbol=row['symbol'], custodian__custodian_code=row['custodian']),
        #         date = row['date'], 
        #         transactionid = row['transactionid'], 
        #         value = row['value'], 
        #         quantity = row['quantity'], 
        #         details = row['details'], 
        #         # unit_cost = row['unit_cost'], 
        #         # profit = row['profit'],
        #     )
        #     for i, row in journal.iterrows()]
        
        # # Upload model_instances to database
        # unique = ['ubo', 'custodian', 'account', 'transactionid', 'operation']
        # update_fields = unique + ['partner', 'instrument', 'date', 'value', 'quantity', 'details', ]
        # Journal.objects.bulk_create(
        #     model_instances, 
        #     ignore_conflicts=True,
        #     # update_conflicts=False,
        #     unique_fields=unique,
        #     update_fields=update_fields,
        # )

        

        # print('TDV journal imported into database, max date imported is', max(all['date']))
        print('Journal exported done', save_file)
