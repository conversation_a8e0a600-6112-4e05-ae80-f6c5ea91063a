# Deposits Service

This document describes the refactored `DepositsService` that replaces the pandas-based `deposit_accruals.py` management command with a Django ORM-based service that automatically creates journal entries.

## Overview

The `DepositsService` provides a clean, maintainable way to:
- Calculate deposit accruals using Django ORM instead of pandas
- Automatically create journal entries in the database
- Handle foreign exchange differences for non-RON deposits
- Support both new deposits and liquidated deposits
- Prevent duplicate journal entries

## Key Improvements

### 1. Django ORM Instead of Pandas
- **Before**: Used pandas DataFrames for data manipulation
- **After**: Uses Django QuerySets and model operations
- **Benefits**: Better integration with Django, type safety, database constraints

### 2. Automatic Journal Entry Creation
- **Before**: Only calculated accruals and saved to Excel
- **After**: Automatically creates `Journal` model instances
- **Benefits**: Data is immediately available in the system, no manual import needed

### 3. Service-Oriented Architecture
- **Before**: Monolithic management command
- **After**: Reusable service class with focused methods
- **Benefits**: Testable, maintainable, reusable across different contexts

### 4. Comprehensive Error Handling
- **Before**: Limited error handling
- **After**: Detailed logging and error reporting
- **Benefits**: Better debugging and monitoring

## Usage

### Basic Usage

```python
from port.services.provider.deposits_service import DepositsService

# Initialize service
service = DepositsService()

# Process all deposits
results = service.calculate_and_store_accruals()

# Process specific deposit
results = service.calculate_and_store_accruals(deposit_symbol='RO96BTRLRONDNEG682594001')

# Get summary without processing
summary = service.get_deposit_summary()
```

### Management Command

```bash
# Process all deposits
python manage.py process_deposits

# Process specific deposit
python manage.py process_deposits --deposit-symbol RO96BTRLRONDNEG682594001

# Show summary only
python manage.py process_deposits --summary-only

# Verbose output
python manage.py process_deposits --verbose
```

## Service Methods

### Main Methods

#### `calculate_and_store_accruals(deposit_symbol=None)`
Main method that processes deposits and creates journal entries.

**Parameters:**
- `deposit_symbol` (str, optional): Process only this specific deposit

**Returns:**
- Dict with processing results including counts and errors

#### `get_deposit_summary(deposit_symbol=None)`
Get summary information about deposits without processing.

**Parameters:**
- `deposit_symbol` (str, optional): Filter by specific deposit

**Returns:**
- Dict with deposit summary information

### Internal Methods

#### `_calculate_accruals_for_deposit(deposit_info, bnr_rates)`
Calculate all accrual entries for a single deposit.

#### `_create_journal_entries(accrual_entries, deposit)`
Create Journal model instances from calculated entries.

#### `_calculate_monthly_accruals(...)`
Calculate monthly accrual entries and FX differences.

#### `_calculate_maturity_entries(...)`
Calculate maturity/liquidation entries.

## Journal Entry Types

The service creates the following types of journal entries:

### 1. Constitution Entries
- `CONSTITUIRE_DEP_VALUTA` - Foreign currency deposit constitution
- `CONSTITUIRE_DEP_LEI` - RON deposit constitution

### 2. Accrual Entries
- `ACCRUAL_DEP_VALUTA` - Foreign currency interest accrual
- `ACCRUAL_DEP_LEI` - RON interest accrual

### 3. FX Difference Entries
- `FX_DEP_VALUTA` - Foreign exchange differences on deposits

### 4. Liquidation Entries
- `INCASARE_DOB_VALUTA` - Foreign currency interest collection
- `INCASARE_DOB_LEI` - RON interest collection
- `AJUSTARE_DOB_VALUTA` - Interest adjustment (calculated vs actual)
- `AJUSTARE_DOB_LEI` - RON interest adjustment
- `LICHIDARE_DEP_VALUTA` - Foreign currency deposit liquidation
- `LICHIDARE_DEP_LEI` - RON deposit liquidation

## Data Flow

1. **Input**: `Deposits` model instances with related `Instrument`, `Currency`, `Custodian`
2. **BNR Rates**: Retrieved from `Bnr` model for FX calculations
3. **Calculations**: Monthly accruals, FX differences, maturity handling
4. **Output**: `Journal` model instances with proper relationships

## Error Handling

The service includes comprehensive error handling:

- **Missing Dependencies**: Validates required UBO exists
- **Data Validation**: Checks for valid dates, rates, amounts
- **Duplicate Prevention**: Prevents creating duplicate journal entries
- **Transaction Safety**: Each deposit is processed independently
- **Detailed Logging**: All operations are logged for debugging

## Testing

The service includes comprehensive test coverage:

```bash
# Run tests
python manage.py test port.tests.test_deposits_service

# Run specific test
python manage.py test port.tests.test_deposits_service.DepositsServiceTestCase.test_calculate_accruals_for_deposit
```

## Configuration

### Required Models
- `Ubo` with code 'DD' must exist
- `Currency` models for all deposit currencies
- `Custodian` models for all deposit custodians
- `Bnr` rates for FX calculations

### Optional Configuration
- Logging level can be adjusted for debugging
- Service can be extended for custom business logic

## Migration from Old Command

### Before (deposit_accruals.py)
```bash
python manage.py deposit_accruals
# Output: Excel file with calculations
# Manual: Import journal entries
```

### After (DepositsService)
```bash
python manage.py process_deposits
# Output: Journal entries created automatically
# Result: Data immediately available in system
```

## Performance Considerations

- **Database Queries**: Optimized with `select_related()` and `prefetch_related()`
- **Bulk Operations**: Uses efficient Django ORM patterns
- **Memory Usage**: Processes deposits individually to avoid memory issues
- **Caching**: BNR rates are cached during processing

## Future Enhancements

Potential improvements for the service:

1. **Batch Processing**: Add support for processing deposits in batches
2. **Async Processing**: Add async support for large datasets
3. **Audit Trail**: Enhanced audit logging for all operations
4. **Custom Rules**: Support for custom accrual calculation rules
5. **Integration**: API endpoints for external system integration

## Troubleshooting

### Common Issues

1. **Missing UBO 'DD'**: Create UBO with code 'DD' in admin
2. **Missing BNR Rates**: Import BNR rates for required dates
3. **Duplicate Entries**: Service prevents duplicates automatically
4. **Permission Errors**: Ensure user has permission to create Journal entries

### Debug Mode

Enable debug logging for detailed operation information:

```python
import logging
logging.getLogger('port.services.provider.deposits_service').setLevel(logging.DEBUG)
```
