#!/usr/bin/env node
"use strict";var t=Object.create;var r=Object.defineProperty;var a=Object.getOwnPropertyDescriptor;var i=Object.getOwnPropertyNames;var g=Object.getPrototypeOf,p=Object.prototype.hasOwnProperty;var d=(e,n,o,s)=>{if(n&&typeof n=="object"||typeof n=="function")for(let c of i(n))!p.call(e,c)&&c!==o&&r(e,c,{get:()=>n[c],enumerable:!(s=a(n,c))||s.enumerable});return e};var h=(e,n,o)=>(o=e!=null?t(g(e)):{},d(n||!e||!e.__esModule?r(o,"default",{value:e,enumerable:!0}):o,e));var l=h(require("chalk"),1);function u(){let e=process.argv.slice(2),o=process.env.npm_execpath||"",s="shadcn@latest";return o.includes("pnpm")?`pnpm dlx ${s}${e.length?` ${e.join(" ")}`:""}`:o.includes("yarn")?`yarn dlx ${s}${e.length?` ${e.join(" ")}`:""}`:o.includes("bun")?`bunx ${s}${e.length?` ${e.join(" ")}`:""}`:`npx ${s}${e.length?` ${e.join(" ")}`:""}`}var $=async()=>{console.log(l.default.yellow("The 'shadcn-ui' package is deprecated. Please use the 'shadcn' package instead:")),console.log(""),console.log(l.default.green(`  ${u()}`)),console.log(""),console.log(l.default.yellow("For more information, visit: https://ui.shadcn.com/docs/cli")),console.log("")};$().catch(e=>{console.error(l.default.red("Error:"),e.message),process.exit(1)});
//# sourceMappingURL=index.cjs.map