from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsA<PERSON><PERSON><PERSON><PERSON>, IsAdminUser
from ibkr_provider.models import IbkrCreds
from ibkr_provider.serializers import IbkrCredsSerializer, IbkrCredsReadSerializer
from tdv_prodiver.models import TDVCreds
from tdv_prodiver.serializers import TDVCredsSerializer, TDVCredsReadSerializer
from ai_hedge_fund_provider.models import AIHedgeFundCreds
from ai_hedge_fund_provider.serializers import AIHedgeFundCredsSerializer, AIHedgeFundCredsReadSerializer


class CredentialsView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get all credentials with masked sensitive data"""
        try:
            # Get IBKR credentials
            ibkr_creds = IbkrCreds.objects.all()
            print(ibkr_creds)
            ibkr_serializer = IbkrCredsReadSerializer(ibkr_creds, many=True)

            # Get TDV credentials
            tdv_creds = TDVCreds.objects.all()
            print(tdv_creds)
            tdv_serializer = TDVCredsReadSerializer(tdv_creds, many=True)

            # Get AI Hedge Fund credentials
            ai_hedge_fund_creds = AIHedgeFundCreds.objects.all()
            print(ai_hedge_fund_creds)
            ai_hedge_fund_serializer = AIHedgeFundCredsReadSerializer(ai_hedge_fund_creds, many=True)

            return Response({
                'ibkr_credentials': ibkr_serializer.data,
                'tdv_credentials': tdv_serializer.data,
                'ai_hedge_fund_credentials': ai_hedge_fund_serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            print(e)
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def post(self, request):
        """Create new credentials"""
        try:
            cred_type = request.data.get('type')

            if not cred_type:
                return Response(
                    {'error': 'Credential type is required (ibkr, tdv, or ai_hedge_fund)'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if cred_type.lower() == 'ibkr':
                serializer = IbkrCredsSerializer(data=request.data)
                if serializer.is_valid():
                    serializer.save()
                    return Response(
                        {'message': 'IBKR credentials created successfully'},
                        status=status.HTTP_201_CREATED
                    )
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            elif cred_type.lower() == 'tdv':
                serializer = TDVCredsSerializer(data=request.data)
                if serializer.is_valid():
                    serializer.save()
                    return Response(
                        {'message': 'TDV credentials created successfully'},
                        status=status.HTTP_201_CREATED
                    )
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )

            elif cred_type.lower() == 'ai_hedge_fund':
                serializer = AIHedgeFundCredsSerializer(data=request.data)
                if serializer.is_valid():
                    serializer.save()
                    return Response(
                        {'message': 'AI Hedge Fund credentials created successfully'},
                        status=status.HTTP_201_CREATED
                    )
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )

            else:
                return Response(
                    {'error': 'Invalid credential type. Use "ibkr", "tdv", or "ai_hedge_fund"'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def put(self, request):
        """Update existing credentials"""
        try:
            cred_type = request.data.get('type')
            cred_id = request.data.get('id')
            
            if not cred_type or not cred_id:
                return Response(
                    {'error': 'Credential type and ID are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if cred_type.lower() == 'ibkr':
                try:
                    cred = IbkrCreds.objects.get(id=cred_id)
                except IbkrCreds.DoesNotExist:
                    return Response(
                        {'error': f'IBKR credentials with ID {cred_id} not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )
                
                serializer = IbkrCredsSerializer(cred, data=request.data, partial=True)
                if serializer.is_valid():
                    serializer.save()
                    return Response(
                        {'message': 'IBKR credentials updated successfully'},
                        status=status.HTTP_200_OK
                    )
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            elif cred_type.lower() == 'tdv':
                try:
                    cred = TDVCreds.objects.get(id=cred_id)
                except TDVCreds.DoesNotExist:
                    return Response(
                        {'error': f'TDV credentials with ID {cred_id} not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )
                
                serializer = TDVCredsSerializer(cred, data=request.data, partial=True)
                if serializer.is_valid():
                    serializer.save()
                    return Response(
                        {'message': 'TDV credentials updated successfully'},
                        status=status.HTTP_200_OK
                    )
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )

            elif cred_type.lower() == 'ai_hedge_fund':
                try:
                    cred = AIHedgeFundCreds.objects.get(id=cred_id)
                except AIHedgeFundCreds.DoesNotExist:
                    return Response(
                        {'error': f'AI Hedge Fund credentials with ID {cred_id} not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )

                serializer = AIHedgeFundCredsSerializer(cred, data=request.data, partial=True)
                if serializer.is_valid():
                    serializer.save()
                    return Response(
                        {'message': 'AI Hedge Fund credentials updated successfully'},
                        status=status.HTTP_200_OK
                    )
                return Response(
                    serializer.errors,
                    status=status.HTTP_400_BAD_REQUEST
                )

            else:
                return Response(
                    {'error': 'Invalid credential type. Use "ibkr", "tdv", or "ai_hedge_fund"'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def delete(self, request):
        """Delete credentials"""
        try:
            cred_type = request.data.get('type')
            cred_id = request.data.get('id')
            
            if not cred_type or not cred_id:
                return Response(
                    {'error': 'Credential type and ID are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if cred_type.lower() == 'ibkr':
                try:
                    cred = IbkrCreds.objects.get(id=cred_id)
                except IbkrCreds.DoesNotExist:
                    return Response(
                        {'error': f'IBKR credentials with ID {cred_id} not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )
                
                cred.delete()
                return Response(
                    {'message': 'IBKR credentials deleted successfully'},
                    status=status.HTTP_200_OK
                )
                
            elif cred_type.lower() == 'tdv':
                try:
                    cred = TDVCreds.objects.get(id=cred_id)
                except TDVCreds.DoesNotExist:
                    return Response(
                        {'error': f'TDV credentials with ID {cred_id} not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )
                
                cred.delete()
                return Response(
                    {'message': 'TDV credentials deleted successfully'},
                    status=status.HTTP_200_OK
                )

            elif cred_type.lower() == 'ai_hedge_fund':
                try:
                    cred = AIHedgeFundCreds.objects.get(id=cred_id)
                except AIHedgeFundCreds.DoesNotExist:
                    return Response(
                        {'error': f'AI Hedge Fund credentials with ID {cred_id} not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )

                cred.delete()
                return Response(
                    {'message': 'AI Hedge Fund credentials deleted successfully'},
                    status=status.HTTP_200_OK
                )

            else:
                return Response(
                    {'error': 'Invalid credential type. Use "ibkr", "tdv", or "ai_hedge_fund"'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )