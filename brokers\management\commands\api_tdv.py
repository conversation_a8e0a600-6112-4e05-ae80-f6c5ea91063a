""" TDV format contabilitate """
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings 

class Command(BaseCommand):

    def handle(self, *args, **options):
        import pandas as pd
        import json
        from datetime import datetime
 
        from .lib.api_tdv import MyWebSocket

        broker = 'tdv'
        today = str(datetime.today())[:10]

        # Subconturi
        pers = settings.TDV_PERS 

        def interogare(pers, functie):
            """ Interogare pe fiecare persoana conform functie si salvare rezultate """
            df = pd.DataFrame()
            
            for firma in pers.keys():
                msg = json.dumps({
                    "cmd": "persoana",
                    "tinta": "selectSubcont",
                    'persoana': pers[firma],
                })
                result = tdv.send_msg(msg) 
                try:
                    dx = functie()
                    if len(dx)>0:
                        dx['firma'] = firma
                        df = pd.concat([df, dx])
                except:
                    pass

            # Export detineri
            save_file = settings.FILE_ROOT + "{broker}/{functie}_{date}.csv".format(broker=broker, functie=functie.__name__, date=today)
            df.to_csv(save_file, index=False)

            return df

        # Interogari
        tdv = MyWebSocket()
        print(pers)
        interogare(pers, tdv.portof)
        interogare(pers, tdv.ordine)
        interogare(pers, tdv.activitate)

        print('Rapoarte TDV exportate', today)

