# AI Hedge Fund - Extractor App Integration Configuration
# Copy this file to .env and configure your extractor app connection

# Extractor App Configuration
EXTRACTOR_URL=http://localhost:8000
EXTRACTOR_USERNAME=your_username
EXTRACTOR_PASSWORD=your_password

# Credential Selection (choose one method)
# Method 1: Use specific credential ID
EXTRACTOR_CREDENTIAL_ID=1

# Method 2: Use credential name (if ID not specified)
# EXTRACTOR_CREDENTIAL_NAME=Production

# Fallback Configuration
# Whether to use local environment variables if extractor app is unavailable
USE_LOCAL_FALLBACK=true

# Cache timeout for credentials (in seconds)
CREDENTIALS_CACHE_TIMEOUT=300

# Local fallback environment variables (used when USE_LOCAL_FALLBACK=true)
# These will be used if the extractor app is unavailable
ANTHROPIC_API_KEY=your-anthropic-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key
GROQ_API_KEY=your-groq-api-key
GOOGLE_API_KEY=your-google-api-key
OPENAI_API_KEY=your-openai-api-key
FINANCIAL_DATASETS_API_KEY=your-financial-datasets-api-key
OPENAI_API_BASE=your-custom-openai-base-url
