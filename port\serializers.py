from rest_framework import serializers
from port.helpers import Accounts<PERSON>elper
from port.models import Account, Account<PERSON><PERSON><PERSON>, BondAccrual, <PERSON><PERSON><PERSON>cy, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ts, <PERSON>rror, Instrument, Journal, Accounting, Operation, Partner, Partner_type, Portfolio, Ubo


# class JournalSerializer(serializers.ModelSerializer, AccountsHelper):
#     debit_analitic = serializers.SerializerMethodField()
#     credit_analitic = serializers.SerializerMethodField()
#     value_abs = serializers.SerializerMethodField()
#     value_ron_abs = serializers.SerializerMethodField()

#     class Meta:
#         model = Journal
#         fields = [
#             'date', 'transactionid', 'details', 'quantity', 'value', 'value_ron', 'bnr',
#             'storno',
#             'ubo', 'custodian', 'account', 'operation', 'partner', 'instrument',
#             'debit_analitic', 'credit_analitic', 'value_abs', 'value_ron_abs',
#         ]
    
#     def get_debit_analitic(self, obj):
#         obj: Journal
#         return self.build_analitic(
#             obj.operation.debit.account_code,
#             obj.instrument.currency.currency_code,
#             obj.custodian.custodian_code + (obj.account.custodian_detail or ''),
#             obj.partner.partner_code,
#             obj.instrument.symbol,
#             credit=False
#         )

#     def get_credit_analitic(self, obj):
#         return self.build_analitic(
#             obj.operation.credit.account_code,
#             obj.instrument.currency.currency_code,
#             obj.custodian.custodian_code + (obj.account.custodian_detail or ''),
#             obj.partner.partner_code,
#             obj.instrument.symbol,
#             credit=True
#         )

#     def get_value_abs(self, obj):
#         return -abs(obj.value) if obj.storno else abs(obj.value)

#     def get_value_ron_abs(self, obj):
#         return -abs(obj.value_ron) if obj.storno else abs(obj.value_ron)


class AccountingSerializer(serializers.ModelSerializer):
    class Meta:
        model = Accounting
        fields = '__all__'


class AccountingWithMappingSerializer(serializers.ModelSerializer, AccountsHelper):
    account_saga = serializers.SerializerMethodField()
    debit_analitic = serializers.SerializerMethodField()
    credit_analitic = serializers.SerializerMethodField()

    class Meta:
        model = Accounting
        fields = [
            'id', 'account_saga', 'debit_analitic', 'credit_analitic'
        ]

    def get_account_saga(self, obj):
        mapping = AccountMapping.objects.filter(main_account=obj).first()
        return mapping.account_saga if mapping else None

    def get_debit_analitic(self, obj: Accounting):
        return self.build_analitic(
            obj.account_code,
            obj.instrument.currency.currency_code,
            obj.custodian.custodian_code + (obj.account.custodian_detail or ''),
            obj.partner.partner_code,
            obj.instrument.symbol,
            credit=False
        )

    def get_credit_analitic(self, obj):
        return self.build_analitic(
            obj,
            currency="EUR",
            custodian_detail="CUST001DETAIL",
            partner="PARTNER1",
            symbol="SYMBOL1",
            credit=True
        )


class AccountMappingDynamicSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccountMapping
        fields = ['id', 'main_account', 'account_saga',]

class JournalSerializer(serializers.ModelSerializer):
    ubo_name = serializers.CharField(source="ubo.ubo_name", read_only=True)
    custodian_name = serializers.CharField(source="custodian.custodian_name", read_only=True)
    account_name = serializers.CharField(source="account.account_name", read_only=True)
    operation_name = serializers.CharField(source="operation.operation_name", read_only=True)
    partner_name = serializers.CharField(source="partner.partner_name", read_only=True)
    instrument_name = serializers.CharField(source="instrument.symbol", read_only=True)

    class Meta:
        model = Journal
        fields = [
            "id", "ubo", "ubo_name", "custodian", "custodian_name",
            "account", "account_name", "operation", "operation_name",
            "partner", "partner_name", "instrument", "instrument_name",
            "date", "transactionid", "value", "value_ron", "bnr",
            "quantity", "details", "storno", "lock"
        ]
        
class JournalCalculatedSerializer(serializers.ModelSerializer):
    ubo_code = serializers.CharField(source="ubo.ubo_code", read_only=True)
    custodian_code = serializers.CharField(source="custodian.custodian_code", read_only=True)
    account_code = serializers.CharField(source="account.account_code", read_only=True)
    operation_code = serializers.CharField(source="operation.operation_code", read_only=True)
    partner_code = serializers.CharField(source="partner.partner_code", read_only=True)
    symbol = serializers.CharField(source="instrument.symbol", read_only=True)
    currency_code = serializers.CharField(source="instrument.currency.currency_code", read_only=True)
    credit_account_code = serializers.CharField(source="operation.credit.account_code", read_only=True)
    credit_account_name = serializers.CharField(source="operation.credit.account_name", read_only=True)
    debit_account_code = serializers.CharField(source="operation.debit.account_code", read_only=True)
    debit_account_name = serializers.CharField(source="operation.debit.account_name", read_only=True)

    credit_analitic = serializers.SerializerMethodField()
    debit_analitic = serializers.SerializerMethodField()
    value_abs = serializers.SerializerMethodField()
    value_ron_abs = serializers.SerializerMethodField()

    class Meta:
        model = Journal
        fields = [
            "id", "date", "transactionid", "quantity", "details",
            "value", "value_ron", "bnr", "storno", "lock",
            "ubo_code", "custodian_code", "account_code",
            "operation_code", "partner_code", "symbol", "currency_code",
            "credit_account_code", "credit_account_name",
            "debit_account_code", "debit_account_name",
            "credit_analitic", "debit_analitic",
            "value_abs", "value_ron_abs"
        ]

    def get_credit_analitic(self, obj):
        return self._build_analitic(obj, credit=True)

    def get_debit_analitic(self, obj):
        return self._build_analitic(obj, credit=False)

    def _build_analitic(self, obj, credit=True):
        try:
            acc_code = obj.operation.credit.account_code if credit else obj.operation.debit.account_code
            mx = Accounting.objects.filter(account_code=acc_code).values().first()
            if not mx:
                return acc_code

            parts = [acc_code]
            dot = '.' if mx.get('has_dot') else ''

            if mx.get('has_currency'):
                parts.append(obj.instrument.currency.currency_code)

            if credit:
                if mx.get('has_custodian_credit'):
                    parts.append(f"{obj.custodian.custodian_code}{obj.account.custodian_detail}")
                if mx.get('has_partner_credit'):
                    parts.append(obj.partner.partner_code)
            else:
                if mx.get('has_custodian_debit'):
                    parts.append(f"{obj.custodian.custodian_code}{obj.account.custodian_detail}")
                if mx.get('has_partner_debit'):
                    parts.append(obj.partner.partner_code)

            if mx.get('has_symbol'):
                parts.append(obj.instrument.symbol)

            # Exception
            if acc_code == '461' and credit and obj.partner.partner_code == 'BBG':
                return '461.BBG'

            return dot.join(parts)
        except Exception as e:
            return str(e)

    def get_value_abs(self, obj):
        return -abs(obj.value) if obj.storno else abs(obj.value)

    def get_value_ron_abs(self, obj):
        return -abs(obj.value_ron) if obj.storno else abs(obj.value_ron)

class InstrumentSerializer(serializers.ModelSerializer):
    custodian_name = serializers.CharField(source='custodian.custodian_name', read_only=True)
    currency_name = serializers.CharField(source='currency.currency_name', read_only=True)

    class Meta:
        model = Instrument
        fields = [
            "id", "symbol", "isin", "custodian", "custodian_name", "currency", "currency_name",
            "name", "type", "principal", "face_value", "interest",
            "depo_start", "bond_issue", "bond_first_coupon", "maturity",
            "convention", "calendar", "bond_coupon_count", "sector", "country", "needs_to_be_checked"
        ]
        

class DepositSerializer(serializers.ModelSerializer):
    deposit = serializers.SerializerMethodField()

    def get_deposit(self, obj):
        return obj.deposit.symbol

    class Meta:
        model = Deposits
        fields = "__all__"

class PartnerSerializer(serializers.ModelSerializer):
    partner_type_code = serializers.CharField(source="partner_type.partner_type_code", read_only=True)
    journal_code = serializers.CharField(source="partner_type.journal_code", read_only=True)

    class Meta:
        model = Partner
        fields = ["id", "partner_code", "partner_name", "partner_type", "partner_type_code", "journal_code"]

class PartnerTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Partner_type
        fields = "__all__"
        
class OperationSerializer(serializers.ModelSerializer):
    debit_display = serializers.SerializerMethodField()
    credit_display = serializers.SerializerMethodField()

    class Meta:
        model = Operation
        fields = ['id', 'operation_code', 'operation_name', 'debit', 'credit', 'debit_display', 'credit_display']

    def get_debit_display(self, obj):
        return f"{obj.debit.account_code} - {obj.debit.account_name}"

    def get_credit_display(self, obj):
        return f"{obj.credit.account_code} - {obj.credit.account_name}"

class AccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = Account
        fields = "__all__"

class CustodianSerializer(serializers.ModelSerializer):
    class Meta:
        model = Custodian
        fields = "__all__"

class AccountingSerializer(serializers.ModelSerializer):
    class Meta:
        model = Accounting
        fields = "__all__"

class CurrencySerializer(serializers.ModelSerializer):
    class Meta:
        model = Currency
        fields = "__all__"


class ErrorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Error
        fields = "__all__"


class PortfolioSerializer(serializers.ModelSerializer):
    ubo_code = serializers.CharField(source="ubo.ubo_code", read_only=True)
    instrument_name = serializers.CharField(source="instrument.symbol", read_only=True)

    class Meta:
        model = Portfolio
        fields = ["id", "ubo_code", "instrument", "instrument_name", "date", "cost", "value", "quantity", "accruedint"]

class UBOSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ubo
        fields = "__all__"

class BondAccrualResultSerializer(serializers.ModelSerializer):
    ubo = serializers.SerializerMethodField()
    custodian = serializers.SerializerMethodField()
    partner = serializers.SerializerMethodField()
    account = serializers.SerializerMethodField()
    instrument = serializers.SerializerMethodField()
    operation = serializers.SerializerMethodField()
    currency = serializers.SerializerMethodField()

    def get_ubo(self, obj):
        return obj.ubo.ubo_code

    def get_custodian(self, obj):
        return obj.custodian.custodian_code

    def get_partner(self, obj):
        return obj.partner.partner_code

    def get_account(self, obj):
        return obj.account.account_code

    def get_instrument(self, obj):
        return obj.instrument.symbol

    def get_operation(self, obj):
        return obj.operation.operation_code

    def get_currency(self, obj):
        return obj.currency.currency_code

    class Meta:
        model = BondAccrual
        fields = '__all__'
