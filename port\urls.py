from django.contrib import admin
from django.urls import include, path

from django.views.generic.base import TemplateView

from port.views.accounting import AccountingViewSet
from port.views.accounts import AccountMappingView, AccountMappingExcelView, AccountsViewSet
from port.views.bonds import BondAccrualResultViewSet
from port.views.currencies import CurrencyViewSet
from port.views.custodians import CustodianViewSet
from port.views.deposits import DepositsViewSet
from port.views.errors import ErrorViewSet
from port.views.journals import JournalViewSet, ExportJournalsExcelView, ExportJournalsDbfView
from port.views.instruments import InstrumentViewSet
from port.views.operations import OperationViewSet
from port.views.partners import PartnerTypeViewSet, PartnerViewSet
from port.views.portfolio import PortfolioViewSet
from port.views.tasks import TriggerBNRFetchView, TriggerJournalsIbkrFetchView, TriggerJournalsTdvFetchView
from port.views.ubos import UBOViewSet

urlpatterns = [
    
    path("", TemplateView.as_view(template_name="home.html"), name="home"),  

    # path('portfolio/', views.portfolio, name='portfolio'),
    # path('port/', views.port, name='port'),
    # path('jurnal/', views.jurnal, name='jurnal'),
    # path('map_operations/', views.map_operations, name='map_operations'),
    path('account-mapping/', AccountMappingView.as_view(), name='account-mapping'),
    path('account-mapping/<int:pk>/', AccountMappingView.as_view(), name='account-mapping-detail'),
    path('account-mapping/excel_file/', AccountMappingExcelView.as_view(), name='excel_file'),
    path('journals/', JournalViewSet.as_view({'get': 'list', 'post': 'create'}), name='journals'),
    path('journals/<int:pk>/', JournalViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='journal-detail'),
    path('journals/', JournalViewSet.as_view({'get': 'list', 'post': 'create'}), name='journals'),
    path('journals/export/excel/', ExportJournalsExcelView.as_view(), name='export-journals-excel'),
    path('journals/export/dbf/', ExportJournalsDbfView.as_view(), name='export-journals-dbf'),
    
    path('instruments/', InstrumentViewSet.as_view({'get': 'list', 'post': 'create'}), name='instruments'),
    path('instruments/<int:pk>/', InstrumentViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='instrument-detail'),
    
    path('deposits/', DepositsViewSet.as_view({'get': 'list', 'post': 'create'}), name='deposits'),
    path('deposits/<int:pk>/', DepositsViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='deposit-detail'),
    
    path('partners/', PartnerViewSet.as_view({'get': 'list', 'post': 'create'}), name='partners'),
    path('partners/<int:pk>/', PartnerViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='partner-detail'),
    
    path('partner-types/', PartnerTypeViewSet.as_view({'get': 'list', 'post': 'create'}), name='partner-types'),
    path('partner-types/<int:pk>/', PartnerTypeViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='partner-type-detail'),
    
    path('operations/', OperationViewSet.as_view({'get': 'list', 'post': 'create'}), name='operations'),
    path('operations/<int:pk>/', OperationViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='operation-detail'),
    
    path('accounts/', AccountsViewSet.as_view({'get': 'list', 'post': 'create'}), name='accounts'),
    path('accounts/<int:pk>/', AccountsViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='account-detail'),
    
    
    path('accountings/', AccountingViewSet.as_view({'get': 'list', 'post': 'create'}), name='accountings'),
    path('accountings/<int:pk>/', AccountingViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='accounting-detail'),
    
    path('custodians/', CustodianViewSet.as_view({'get': 'list', 'post': 'create'}), name='custodians'),
    path('custodians/<int:pk>/', CustodianViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='custodian-detail'),
    
    path('currencies/', CurrencyViewSet.as_view({'get': 'list', 'post': 'create'}), name='currencies'),
    path('currencies/<int:pk>/', CurrencyViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='currency-detail'),
    
    path('portfolios/', PortfolioViewSet.as_view({'get': 'list', 'post': 'create'}), name='portfolios'),
    path('portfolios/<int:pk>/', PortfolioViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='portfolio-detail'),
    
    path('ubos/', UBOViewSet.as_view({'get': 'list', 'post': 'create'}), name='ubos'),
    path('ubos/<int:pk>/', UBOViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='ubos-detail'),
    
    path('errors/', ErrorViewSet.as_view({'get': 'list', 'post': 'create'}), name='errors'),
    path('errors/<int:pk>/', ErrorViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='error-detail'),
    
    path('trigger-journals-tdv-fetch/', TriggerJournalsTdvFetchView.as_view(), name='trigger-journals-tdv-fetch/'),
    path('trigger-journals-ibkr-fetch/', TriggerJournalsIbkrFetchView.as_view(), name='trigger-journals-ibkr-fetch/'),
    path('trigger-bnr-fetch/', TriggerBNRFetchView.as_view(), name='trigger-bnr-fetch/'),
    
    path('bond-accruals/', BondAccrualResultViewSet.as_view({'get': 'list', 'post': 'create'}), name='bond-accruals'),
    path('bond-accruals/<int:pk>/', BondAccrualResultViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'delete': 'destroy'
    }), name='bond-accrual-detail'),
    path('bond-accruals/recalculate/', BondAccrualResultViewSet.as_view({'post': 'recalculate_and_save'}), name='bond-accrual-recalculate'),
]
