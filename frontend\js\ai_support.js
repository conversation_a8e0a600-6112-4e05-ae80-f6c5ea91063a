import { AI_HEDGE_FUND_URL } from '/js/constants.js';

console.log('AI Hedge Fund URL:', AI_HEDGE_FUND_URL);

class AIHedgeFund {
    constructor() {
        this.eventSource = null;
        this.isRunning = false;
        this.agents = [];
        this.models = [];
        this.init();
    }

    async init() {
        this.setupEventListeners();
        this.setDefaultDates();
        await this.loadAgents();
        await this.loadModels();
    }

    setupEventListeners() {
        const form = document.getElementById('hedgeFundForm');
        const stopBtn = document.getElementById('stopAnalysisBtn');
        const modelProvider = document.getElementById('modelProvider');

        if (form) form.addEventListener('submit', (e) => this.handleSubmit(e));
        if (stopBtn) stopBtn.addEventListener('click', () => this.stopAnalysis());
        if (modelProvider) modelProvider.addEventListener('change', () => this.updateModelOptions());
    }

    setDefaultDates() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 90); // 90 days ago

        const endDateInput = document.getElementById('endDate');
        const startDateInput = document.getElementById('startDate');

        if (endDateInput) endDateInput.value = endDate.toISOString().split('T')[0];
        if (startDateInput) startDateInput.value = startDate.toISOString().split('T')[0];
    }

    async loadAgents() {
        try {
            const response = await fetch(`${AI_HEDGE_FUND_URL}/hedge-fund/agents`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log("AGENTS RESPONSE:")
            console.log(data)
            this.agents = data.agents || [];
            this.renderAgents();
        } catch (error) {
            console.error('Failed to load agents:', error);
            this.showError('Failed to load available agents. Make sure AI Hedge Fund backend is running.');
        }
    }

    async loadModels() {
        try {
            const response = await fetch(`${AI_HEDGE_FUND_URL}/hedge-fund/language-models`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            this.models = data.models || [];
            this.updateModelOptions();
        } catch (error) {
            console.error('Failed to load models:', error);
            this.showError('Failed to load available models. Make sure AI Hedge Fund backend is running.');
        }
    }

    renderAgents() {
        const container = document.getElementById('agentsContainer');
        if (!container) return;

        if (this.agents.length === 0) {
            console.log("AGENTS RESPONSE:", this.agents);

            // Provide fallback agents when backend is not available
            container.innerHTML = `
                <div class="alert alert-warning mb-3">
                    <strong>Backend not connected</strong><br>
                    <small>Using demo agents. Start the AI Hedge Fund backend on port 8001 for full functionality.</small>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="technical_analyst" id="agent_technical" checked>
                    <label class="form-check-label" for="agent_technical">
                        <strong>Technical Analyst (Demo)</strong><br>
                        <small class="text-muted">Analyzes price patterns and technical indicators</small>
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="fundamental_analyst" id="agent_fundamental" checked>
                    <label class="form-check-label" for="agent_fundamental">
                        <strong>Fundamental Analyst (Demo)</strong><br>
                        <small class="text-muted">Evaluates company financials and market conditions</small>
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="sentiment_analyst" id="agent_sentiment" checked>
                    <label class="form-check-label" for="agent_sentiment">
                        <strong>Sentiment Analyst (Demo)</strong><br>
                        <small class="text-muted">Analyzes market sentiment and news</small>
                    </label>
                </div>
            `;
            return;
        }

        const agentsHtml = this.agents.map(agent => `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="${agent.id}"
                        id="agent_${agent.id}" checked>
                <label class="form-check-label" for="agent_${agent.id}">
                    <strong>${agent.display_name}</strong>
                    <br><small class="text-muted">${agent.description}</small>
                </label>
            </div>
        `).join('');

        container.innerHTML = agentsHtml;
    }

    updateModelOptions() {
        const provider = document.getElementById('modelProvider')?.value;
        const modelSelect = document.getElementById('modelName');
        if (!provider || !modelSelect) return;

        // Filter models by provider
        const providerModels = this.models.filter(model =>
            model.provider.toUpperCase() === provider
        );

        modelSelect.innerHTML = providerModels.map(model =>
            `<option value="${model.name}">${model.display_name || model.name}</option>`
        ).join('');
    }

    async handleSubmit(e) {
        e.preventDefault();

        if (this.isRunning) {
            this.showError('Analysis is already running');
            return;
        }

        const formData = this.getFormData();
        if (!this.validateFormData(formData)) {
            return;
        }

        this.startAnalysis(formData);
    }

    getFormData() {
        const selectedAgents = Array.from(
            document.querySelectorAll('#agentsContainer input[type="checkbox"]:checked')
        ).map(cb => cb.value);

        const tickersInput = document.getElementById('tickers');
        const initialCashInput = document.getElementById('initialCash');
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        const modelNameInput = document.getElementById('modelName');
        const modelProviderInput = document.getElementById('modelProvider');

        return {
            tickers: tickersInput ? tickersInput.value.split(',').map(t => t.trim()) : [],
            selected_agents: selectedAgents,
            start_date: startDateInput ? startDateInput.value : '',
            end_date: endDateInput ? endDateInput.value : '',
            model_name: modelNameInput ? modelNameInput.value : 'gpt-4',
            model_provider: modelProviderInput ? modelProviderInput.value : 'OPENAI',
            initial_cash: initialCashInput ? parseFloat(initialCashInput.value) : 100000,
            margin_requirement: 0.0
        };
    }

    validateFormData(data) {
        if (data.tickers.length === 0 || data.tickers[0] === '') {
            this.showError('Please enter at least one stock ticker');
            return false;
        }

        if (data.selected_agents.length === 0) {
            this.showError('Please select at least one AI agent');
            return false;
        }

        if (data.initial_cash < 1000) {
            this.showError('Initial cash must be at least $1,000');
            return false;
        }

        return true;
    }

    showError(message) {
        const container = document.getElementById('statusContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                <p>${message}</p>
            </div>
        `;
        console.error('AI Hedge Fund Error:', message);
    }

    updateStatus(message, type = 'info') {
        const container = document.getElementById('statusContainer');
        if (!container) return;

        const iconClass = type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle';
        const textClass = type === 'success' ? 'text-success' :
                            type === 'error' ? 'text-danger' : 'text-info';

        container.innerHTML = `
            <div class="text-center ${textClass}">
                <i class="fas ${iconClass} fa-2x mb-2"></i>
                <p>${message}</p>
            </div>
        `;
    }
}


let aiInstance = null;
function maybeInitAIHedgeFund() {
    if (!aiInstance) {
        aiInstance = new AIHedgeFund();
    }
}

document.addEventListener("DOMContentLoaded", () => {
    maybeInitAIHedgeFund();
});

document.getElementById("hedge-fund-tab")?.addEventListener("shown.bs.tab", () => {
    maybeInitAIHedgeFund();
});

maybeInitAIHedgeFund();

// // Initialize when DOM is loaded
// document.addEventListener('DOMContentLoaded', () => {
//     // Only initialize if we're on the hedge fund tab
//     console.log("Creating AIHedgeFund instance")
//     const hedgeFundTab = document.getElementById('hedge-fund');
//     console.log(hedgeFundTab)
//     if (hedgeFundTab && hedgeFundTab.classList.contains('active')) {
//         console.log("ENTERED")
//         new AIHedgeFund();
//     }
// });

// // Initialize when hedge fund tab is shown
// const hedgeFundTabButton = document.getElementById('hedge-fund-tab');
// if (hedgeFundTabButton) {
//     hedgeFundTabButton.addEventListener('shown.bs.tab', () => {
//         new AIHedgeFund();
//     });
// }
