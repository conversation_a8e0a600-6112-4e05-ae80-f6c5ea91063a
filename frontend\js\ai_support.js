import { AI_HEDGE_FUND_URL } from '/js/constants.js';

console.log('AI Hedge Fund URL:', AI_HEDGE_FUND_URL);

class AIHedgeFund {
    constructor() {
        this.eventSource = null;
        this.isRunning = false;
        this.agents = [];
        this.models = [];
        this.init();
    }

    async init() {
        this.setupEventListeners();
        this.setDefaultDates();
        await this.loadAgents();
        await this.loadModels();
    }

    setupEventListeners() {
        const form = document.getElementById('hedgeFundForm');
        const stopBtn = document.getElementById('stopAnalysisBtn');
        const modelProvider = document.getElementById('modelProvider');

        if (form) form.addEventListener('submit', (e) => this.handleSubmit(e));
        if (stopBtn) stopBtn.addEventListener('click', () => this.stopAnalysis());
        if (modelProvider) modelProvider.addEventListener('change', () => this.updateModelOptions());
    }

    setDefaultDates() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 90); // 90 days ago

        const endDateInput = document.getElementById('endDate');
        const startDateInput = document.getElementById('startDate');

        if (endDateInput) endDateInput.value = endDate.toISOString().split('T')[0];
        if (startDateInput) startDateInput.value = startDate.toISOString().split('T')[0];
    }

    async loadAgents() {
        const container = document.getElementById('agentsContainer');
        if (!container) {
            console.error('agentsContainer not found');
            return;
        }

        try {
            console.log('Attempting to load agents from:', `${AI_HEDGE_FUND_URL}/hedge-fund/agents`);

            const response = await fetch(`${AI_HEDGE_FUND_URL}/hedge-fund/agents`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });

            console.log('Response status:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Agents data received:', data);

            this.agents = data.agents || [];
            this.renderAgents();
        } catch (error) {
            console.error('Failed to load agents:', error);
            this.agents = []; // Ensure agents array is empty for fallback
            this.renderAgents(); // This will show the fallback demo agents
        }
    }

    async loadModels() {
        try {
            const response = await fetch(`${AI_HEDGE_FUND_URL}/hedge-fund/language-models`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            this.models = data.models || [];
            this.updateModelOptions();
        } catch (error) {
            console.error('Failed to load models:', error);
            // Use default models if backend is not available
            this.models = [
                { name: 'gpt-4', provider: 'OPENAI', display_name: 'GPT-4' },
                { name: 'gpt-3.5-turbo', provider: 'OPENAI', display_name: 'GPT-3.5 Turbo' },
                { name: 'claude-3-sonnet', provider: 'ANTHROPIC', display_name: 'Claude 3 Sonnet' },
                { name: 'llama-3-70b', provider: 'GROQ', display_name: 'Llama 3 70B' }
            ];
            this.updateModelOptions();
        }
    }

    renderAgents() {
        const container = document.getElementById('agentsContainer');
        if (!container) return;

        if (this.agents.length === 0) {
            // Provide fallback agents when backend is not available
            container.innerHTML = `
                <div class="alert alert-warning mb-3">
                    <strong>Backend not connected</strong><br>
                    <small>Using demo agents. Start the AI Hedge Fund backend on port 8001 for full functionality.</small>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="technical_analyst" id="agent_technical" checked>
                    <label class="form-check-label" for="agent_technical">
                        <strong>Technical Analyst (Demo)</strong><br>
                        <small class="text-muted">Analyzes price patterns and technical indicators</small>
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="fundamental_analyst" id="agent_fundamental" checked>
                    <label class="form-check-label" for="agent_fundamental">
                        <strong>Fundamental Analyst (Demo)</strong><br>
                        <small class="text-muted">Evaluates company financials and market conditions</small>
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="sentiment_analyst" id="agent_sentiment" checked>
                    <label class="form-check-label" for="agent_sentiment">
                        <strong>Sentiment Analyst (Demo)</strong><br>
                        <small class="text-muted">Analyzes market sentiment and news</small>
                    </label>
                </div>
            `;
            return;
        }

        const agentsHtml = this.agents.map(agent => `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="${agent.id}"
                        id="agent_${agent.id}" checked>
                <label class="form-check-label" for="agent_${agent.id}">
                    <strong>${agent.name}</strong>
                    <br><small class="text-muted">${agent.description}</small>
                </label>
            </div>
        `).join('');

        container.innerHTML = agentsHtml;
    }

    updateModelOptions() {
        const provider = document.getElementById('modelProvider')?.value;
        const modelSelect = document.getElementById('modelName');
        if (!provider || !modelSelect) return;

        // Filter models by provider
        const providerModels = this.models.filter(model =>
            model.provider.toUpperCase() === provider
        );

        modelSelect.innerHTML = providerModels.map(model =>
            `<option value="${model.name}">${model.display_name || model.name}</option>`
        ).join('');
    }

    async handleSubmit(e) {
        e.preventDefault();

        if (this.isRunning) {
            this.showError('Analysis is already running');
            return;
        }

        const formData = this.getFormData();
        if (!this.validateFormData(formData)) {
            return;
        }

        this.startAnalysis(formData);
    }

    getFormData() {
        const selectedAgents = Array.from(
            document.querySelectorAll('#agentsContainer input[type="checkbox"]:checked')
        ).map(cb => cb.value);

        const tickersInput = document.getElementById('tickers');
        const initialCashInput = document.getElementById('initialCash');
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        const modelNameInput = document.getElementById('modelName');
        const modelProviderInput = document.getElementById('modelProvider');

        return {
            tickers: tickersInput ? tickersInput.value.split(',').map(t => t.trim()) : [],
            selected_agents: selectedAgents,
            start_date: startDateInput ? startDateInput.value : '',
            end_date: endDateInput ? endDateInput.value : '',
            model_name: modelNameInput ? modelNameInput.value : 'gpt-4',
            model_provider: modelProviderInput ? modelProviderInput.value : 'OPENAI',
            initial_cash: initialCashInput ? parseFloat(initialCashInput.value) : 100000,
            margin_requirement: 0.0
        };
    }

    validateFormData(data) {
        if (data.tickers.length === 0 || data.tickers[0] === '') {
            this.showError('Please enter at least one stock ticker');
            return false;
        }

        if (data.selected_agents.length === 0) {
            this.showError('Please select at least one AI agent');
            return false;
        }

        if (data.initial_cash < 1000) {
            this.showError('Initial cash must be at least $1,000');
            return false;
        }

        return true;
    }

    async startAnalysis(formData) {
        this.isRunning = true;
        this.updateUI(true);
        this.clearResults();
        this.showProgress();

        try {
            const response = await fetch(`${AI_HEDGE_FUND_URL}/hedge-fund/run`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            this.setupEventStream(response);
        } catch (error) {
            console.error('Failed to start analysis:', error);
            this.showError('Failed to start analysis: ' + error.message);
            this.stopAnalysis();
        }
    }

    setupEventStream(response) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        const readStream = async () => {
            try {
                while (true) {
                    const { done, value } = await reader.read();

                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                this.handleStreamEvent(data);
                            } catch (e) {
                                console.error('Failed to parse event data:', e);
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('Stream reading error:', error);
                this.showError('Connection lost during analysis');
            } finally {
                this.stopAnalysis();
            }
        };

        readStream();
    }

    handleStreamEvent(event) {
        switch (event.type) {
            case 'start':
                this.updateStatus('Analysis started', 'info');
                break;
            case 'progress':
                this.addProgressUpdate(event);
                break;
            case 'complete':
                this.showResults(event.data);
                this.updateStatus('Analysis completed successfully', 'success');
                break;
            case 'error':
                this.showError(event.message);
                break;
        }
    }

    addProgressUpdate(event) {
        const container = document.getElementById('progressContainer');
        if (!container) return;

        const timestamp = new Date(event.timestamp).toLocaleTimeString();

        const progressHtml = `
            <div class="progress-item mb-2 p-2 border-start border-3 border-primary">
                <div class="d-flex justify-content-between">
                    <strong>${event.agent}</strong>
                    <small class="text-muted">${timestamp}</small>
                </div>
                <div class="text-muted">${event.ticker}: ${event.status}</div>
                ${event.analysis ? `<div class="mt-1"><small>${event.analysis}</small></div>` : ''}
            </div>
        `;

        container.insertAdjacentHTML('beforeend', progressHtml);
        container.scrollTop = container.scrollHeight;
    }

    showResults(data) {
        const resultsPanel = document.getElementById('resultsPanel');
        if (resultsPanel) resultsPanel.style.display = 'block';

        // Display decisions
        const decisionsContainer = document.getElementById('decisionsContainer');
        if (decisionsContainer) {
            decisionsContainer.innerHTML = this.formatDecisions(data.decisions);
        }

        // Display signals
        const signalsContainer = document.getElementById('signalsContainer');
        if (signalsContainer) {
            signalsContainer.innerHTML = this.formatSignals(data.analyst_signals);
        }
    }

    formatDecisions(decisions) {
        if (!decisions || Object.keys(decisions).length === 0) {
            return '<div class="text-muted">No investment decisions available</div>';
        }

        return Object.entries(decisions).map(([ticker, decision]) => `
            <div class="decision-item mb-3 p-3 border rounded">
                <h6 class="text-primary">${ticker}</h6>
                <div class="row">
                    <div class="col-6">
                        <strong>Action:</strong>
                        <span class="badge ${this.getActionBadgeClass(decision.action)}">${decision.action}</span>
                    </div>
                    <div class="col-6">
                        <strong>Quantity:</strong> ${decision.quantity || 'N/A'}
                    </div>
                </div>
                ${decision.reasoning ? `<div class="mt-2"><small>${decision.reasoning}</small></div>` : ''}
            </div>
        `).join('');
    }

    formatSignals(signals) {
        if (!signals || Object.keys(signals).length === 0) {
            return '<div class="text-muted">No analyst signals available</div>';
        }

        return Object.entries(signals).map(([agent, agentSignals]) => `
            <div class="signal-item mb-3 p-3 border rounded">
                <h6 class="text-info">${agent}</h6>
                ${Object.entries(agentSignals).map(([ticker, signal]) => `
                    <div class="mb-2">
                        <strong>${ticker}:</strong>
                        <span class="badge ${this.getSignalBadgeClass(signal.signal)}">${signal.signal}</span>
                        ${signal.confidence ? `<small class="text-muted">(${signal.confidence}% confidence)</small>` : ''}
                    </div>
                `).join('')}
            </div>
        `).join('');
    }

    getActionBadgeClass(action) {
        switch (action?.toLowerCase()) {
            case 'buy': return 'bg-success';
            case 'sell': return 'bg-danger';
            case 'hold': return 'bg-warning';
            default: return 'bg-secondary';
        }
    }

    getSignalBadgeClass(signal) {
        switch (signal?.toLowerCase()) {
            case 'bullish': return 'bg-success';
            case 'bearish': return 'bg-danger';
            case 'neutral': return 'bg-warning';
            default: return 'bg-secondary';
        }
    }

    stopAnalysis() {
        this.isRunning = false;
        this.updateUI(false);

        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    updateUI(running) {
        const runBtn = document.getElementById('runAnalysisBtn');
        const stopBtn = document.getElementById('stopAnalysisBtn');

        if (runBtn) {
            runBtn.disabled = running;
            runBtn.innerHTML = running ?
                '<span class="spinner-border spinner-border-sm me-2"></span>Running...' :
                '<i class="fas fa-play me-2"></i>Run Analysis';
        }

        if (stopBtn) {
            stopBtn.disabled = !running;
        }
    }

    showProgress() {
        const progressPanel = document.getElementById('progressPanel');
        const progressContainer = document.getElementById('progressContainer');

        if (progressPanel) progressPanel.style.display = 'block';
        if (progressContainer) progressContainer.innerHTML = '';
    }

    clearResults() {
        const resultsPanel = document.getElementById('resultsPanel');
        const decisionsContainer = document.getElementById('decisionsContainer');
        const signalsContainer = document.getElementById('signalsContainer');

        if (resultsPanel) resultsPanel.style.display = 'none';
        if (decisionsContainer) decisionsContainer.innerHTML = '';
        if (signalsContainer) signalsContainer.innerHTML = '';
    }

    updateStatus(message, type = 'info') {
        const container = document.getElementById('statusContainer');
        if (!container) return;

        const iconClass = type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle';
        const textClass = type === 'success' ? 'text-success' :
                            type === 'error' ? 'text-danger' : 'text-info';

        container.innerHTML = `
            <div class="text-center ${textClass}">
                <i class="fas ${iconClass} fa-2x mb-2"></i>
                <p>${message}</p>
            </div>
        `;
    }

    showError(message) {
        this.updateStatus(message, 'error');
        console.error('AI Hedge Fund Error:', message);
    }
}


let aiInstance = null;
function maybeInitAIHedgeFund() {
    if (!aiInstance) {
        aiInstance = new AIHedgeFund();
    }
}

document.addEventListener("DOMContentLoaded", () => {
    maybeInitAIHedgeFund();
});

document.getElementById("hedge-fund-tab")?.addEventListener("shown.bs.tab", () => {
    maybeInitAIHedgeFund();
});

maybeInitAIHedgeFund();

// // Initialize when DOM is loaded
// document.addEventListener('DOMContentLoaded', () => {
//     // Only initialize if we're on the hedge fund tab
//     console.log("Creating AIHedgeFund instance")
//     const hedgeFundTab = document.getElementById('hedge-fund');
//     console.log(hedgeFundTab)
//     if (hedgeFundTab && hedgeFundTab.classList.contains('active')) {
//         console.log("ENTERED")
//         new AIHedgeFund();
//     }
// });

// // Initialize when hedge fund tab is shown
// const hedgeFundTabButton = document.getElementById('hedge-fund-tab');
// if (hedgeFundTabButton) {
//     hedgeFundTabButton.addEventListener('shown.bs.tab', () => {
//         new AIHedgeFund();
//     });
// }
