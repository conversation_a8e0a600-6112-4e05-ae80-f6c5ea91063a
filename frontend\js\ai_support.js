async function loadHedgeFundContent() {
    try {
        const response = await fetch('/views/ai_hedge_fund.html');
        const content = await response.text();
        document.getElementById('ai-hedge-fund-content').innerHTML = content;

        // Load the AI hedge fund JavaScript
        const script = document.createElement('script');
        script.type = 'module';
        script.src = '/js/ai_hedge_fund.js';
        document.head.appendChild(script);
    } catch (error) {
        console.error('Failed to load AI hedge fund content:', error);
        document.getElementById('ai-hedge-fund-content').innerHTML =
            '<div class="alert alert-danger">Failed to load AI hedge fund interface</div>';
    }
}

// Load content when tab is shown
document.getElementById('hedge-fund-tab').addEventListener('shown.bs.tab', loadHedgeFundContent);

// Load immediately if this tab is active
if (document.getElementById('hedge-fund-tab').classList.contains('active')) {
    loadHedgeFundContent();
}