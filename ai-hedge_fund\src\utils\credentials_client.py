"""
Credentials client for fetching API keys from the extractor app
"""
import os
import requests
import logging
from typing import Dict, Optional, Any
from dataclasses import dataclass
from functools import lru_cache

logger = logging.getLogger(__name__)


@dataclass
class CredentialsConfig:
    """Configuration for credentials client"""
    extractor_url: str
    username: str
    password: str
    credential_id: Optional[int] = None
    credential_name: Optional[str] = None
    use_local_fallback: bool = True
    cache_timeout: int = 300  # 5 minutes


class CredentialsClient:
    """Client for fetching credentials from the extractor app"""
    
    def __init__(self, config: CredentialsConfig):
        self.config = config
        self.session = requests.Session()
        self._access_token = None
        self._credentials_cache = None
        self._cache_timestamp = 0
        
    def _authenticate(self) -> bool:
        """Authenticate with the extractor app"""
        try:
            auth_url = f"{self.config.extractor_url}/auth/login/"
            response = self.session.post(auth_url, json={
                'username': self.config.username,
                'password': self.config.password
            })
            
            if response.status_code == 200:
                data = response.json()
                self._access_token = data.get('access')
                if self._access_token:
                    self.session.headers.update({
                        'Authorization': f'Bearer {self._access_token}'
                    })
                    logger.info("Successfully authenticated with extractor app")
                    return True
            
            logger.error(f"Authentication failed: {response.status_code} - {response.text}")
            return False
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False
    
    def _fetch_credentials_from_api(self) -> Optional[Dict[str, Any]]:
        """Fetch credentials from the extractor app API"""
        try:
            # Authenticate if needed
            if not self._access_token:
                if not self._authenticate():
                    return None
            
            # Fetch credentials
            creds_url = f"{self.config.extractor_url}/credentials/"
            response = self.session.get(creds_url)
            
            if response.status_code == 401:
                # Token expired, re-authenticate
                logger.info("Token expired, re-authenticating...")
                if self._authenticate():
                    response = self.session.get(creds_url)
                else:
                    return None
            
            if response.status_code == 200:
                data = response.json()
                ai_creds = data.get('ai_hedge_fund_credentials', [])
                
                if not ai_creds:
                    logger.warning("No AI hedge fund credentials found in extractor app")
                    return None
                
                # Find the specific credential set
                target_cred = None
                
                if self.config.credential_id:
                    target_cred = next((c for c in ai_creds if c['id'] == self.config.credential_id), None)
                elif self.config.credential_name:
                    target_cred = next((c for c in ai_creds if c['name'] == self.config.credential_name), None)
                else:
                    # Use the first active credential set
                    target_cred = next((c for c in ai_creds if c.get('is_active', True)), None)
                
                if target_cred:
                    logger.info(f"Found credentials: {target_cred['name']} (ID: {target_cred['id']})")
                    return self._fetch_full_credentials(target_cred['id'])
                else:
                    logger.warning("No matching AI hedge fund credentials found")
                    return None
            
            logger.error(f"Failed to fetch credentials: {response.status_code} - {response.text}")
            return None
            
        except Exception as e:
            logger.error(f"Error fetching credentials: {e}")
            return None
    
    def _fetch_full_credentials(self, cred_id: int) -> Optional[Dict[str, str]]:
        """Fetch full credentials (with actual API keys) for export"""
        try:
            export_url = f"{self.config.extractor_url}/ai-hedge-fund/ai-hedge-fund-creds/export/{cred_id}/"
            response = self.session.get(export_url)
            
            if response.status_code == 200:
                # Parse the .env content
                env_content = response.text
                credentials = {}
                
                for line in env_content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        credentials[key.strip()] = value.strip()
                
                logger.info(f"Successfully fetched {len(credentials)} credential values")
                return credentials
            
            logger.error(f"Failed to export credentials: {response.status_code}")
            return None
            
        except Exception as e:
            logger.error(f"Error exporting credentials: {e}")
            return None
    
    def _get_local_fallback(self) -> Dict[str, str]:
        """Get credentials from local environment variables as fallback"""
        if not self.config.use_local_fallback:
            return {}
        
        logger.info("Using local environment variables as fallback")
        
        env_vars = [
            'ANTHROPIC_API_KEY',
            'DEEPSEEK_API_KEY', 
            'GROQ_API_KEY',
            'GOOGLE_API_KEY',
            'OPENAI_API_KEY',
            'FINANCIAL_DATASETS_API_KEY',
            'OPENAI_API_BASE'
        ]
        
        credentials = {}
        for var in env_vars:
            value = os.getenv(var)
            if value:
                credentials[var] = value
        
        logger.info(f"Found {len(credentials)} local environment variables")
        return credentials
    
    @lru_cache(maxsize=1)
    def get_credentials(self, force_refresh: bool = False) -> Dict[str, str]:
        """
        Get credentials with caching
        
        Args:
            force_refresh: Force refresh from API, bypass cache
            
        Returns:
            Dictionary of environment variable names to values
        """
        import time
        
        current_time = time.time()
        
        # Check cache validity
        if (not force_refresh and 
            self._credentials_cache and 
            (current_time - self._cache_timestamp) < self.config.cache_timeout):
            logger.debug("Using cached credentials")
            return self._credentials_cache
        
        # Try to fetch from API
        credentials = self._fetch_credentials_from_api()
        
        # Fallback to local environment if API fails
        if not credentials:
            logger.warning("Failed to fetch from extractor app, using fallback")
            credentials = self._get_local_fallback()
        
        # Cache the result
        if credentials:
            self._credentials_cache = credentials
            self._cache_timestamp = current_time
            # Clear the LRU cache to force refresh
            self.get_credentials.cache_clear()
        
        return credentials or {}
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """
        Get API key for a specific provider
        
        Args:
            provider: Provider name (openai, anthropic, groq, google, deepseek, financial_datasets)
            
        Returns:
            API key string or None if not found
        """
        credentials = self.get_credentials()
        
        provider_mapping = {
            'openai': 'OPENAI_API_KEY',
            'anthropic': 'ANTHROPIC_API_KEY', 
            'groq': 'GROQ_API_KEY',
            'google': 'GOOGLE_API_KEY',
            'deepseek': 'DEEPSEEK_API_KEY',
            'financial_datasets': 'FINANCIAL_DATASETS_API_KEY'
        }
        
        env_var = provider_mapping.get(provider.lower())
        if env_var:
            return credentials.get(env_var)
        
        logger.warning(f"Unknown provider: {provider}")
        return None
    
    def get_openai_base_url(self) -> Optional[str]:
        """Get custom OpenAI base URL if configured"""
        credentials = self.get_credentials()
        return credentials.get('OPENAI_API_BASE')
    
    def refresh_credentials(self):
        """Force refresh credentials from the API"""
        logger.info("Forcing credentials refresh")
        self.get_credentials(force_refresh=True)


# Global credentials client instance
_credentials_client: Optional[CredentialsClient] = None


def get_credentials_client() -> CredentialsClient:
    """Get the global credentials client instance"""
    global _credentials_client

    if _credentials_client is None:
        # Initialize from environment variables
        config = CredentialsConfig(
            extractor_url=os.getenv('NCH_EXTR_URL', 'http://localhost:8000'),
            username=os.getenv('NCH_EXTR_LOGIN_USER', ''),
            password=os.getenv('NCH_EXTR_LOGIN_PASS', ''),
            credential_id=int(os.getenv('EXTRACTOR_CREDENTIAL_ID', 0)) or None,
            credential_name=os.getenv('EXTRACTOR_CREDENTIAL_NAME', ''),
            use_local_fallback=os.getenv('USE_LOCAL_FALLBACK', 'true').lower() == 'true',
            cache_timeout=int(os.getenv('CREDENTIALS_CACHE_TIMEOUT', '300'))
        )
        
        _credentials_client = CredentialsClient(config)
    
    return _credentials_client


def get_api_key(provider: str) -> Optional[str]:
    """Convenience function to get API key for a provider"""
    client = get_credentials_client()
    return client.get_api_key(provider)
