{"name": "shadcn-ui", "version": "0.9.5", "description": "Add components to your apps.", "publishConfig": {"access": "public"}, "license": "MIT", "author": {"name": "shadcn", "url": "https://twitter.com/shadcn"}, "repository": {"type": "git", "url": "https://github.com/shadcn/ui.git", "directory": "packages/cli"}, "files": ["dist"], "keywords": ["components", "ui", "tailwind", "shadcn"], "type": "module", "exports": "./dist/index.js", "bin": "./dist/index.js", "scripts": {"dev": "tsup --watch", "build": "tsup", "typecheck": "tsc --noEmit", "start:dev": "cross-env COMPONENTS_REGISTRY_URL=http://localhost:3003 node dist/index.js", "start": "node dist/index.js", "format:write": "prettier --write \"**/*.{ts,tsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,mdx}\" --cache", "test": "vitest run"}, "dependencies": {"chalk": "^5.4.1"}, "devDependencies": {"tsup": "^6.6.3"}}