[tool.poetry]
name = "ai-hedge-fund"
version = "0.1.0"
description = "An AI-powered hedge fund that uses multiple agents to make trading decisions"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [
    { include = "src", from = "." },
    { include = "app", from = "." }
]

[tool.poetry.dependencies]
python = "^3.11"
langchain = "0.3.0"
langchain-anthropic = "0.3.5"
langchain-groq = "0.2.3"
langchain-openai = "^0.3.5"
langchain-deepseek = "^0.1.2"
langchain-ollama = "^0.2.0"
langgraph = "0.2.56"
pandas = "^2.1.0"
numpy = "^1.24.0"
python-dotenv = "1.0.0"
matplotlib = "^3.9.2"
tabulate = "^0.9.0"
colorama = "^0.4.6"
questionary = "^2.1.0"
rich = "^13.9.4"
langchain-google-genai = "^2.0.11"
# Backend dependencies
fastapi = {extras = ["standard"], version = "^0.104.0"}
fastapi-cli = "^0.0.7"
pydantic = "^2.4.2"
httpx = "^0.27.0"
sqlalchemy = "^2.0.22"
alembic = "^1.12.0"
psycopg2-binary = "^2.9.7"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
black = "^23.7.0"
isort = "^5.12.0"
flake8 = "^6.1.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 420
target-version = ['py311']
include = '\.pyi?$'

[tool.isort]
profile = "black"
force_alphabetical_sort_within_sections = true