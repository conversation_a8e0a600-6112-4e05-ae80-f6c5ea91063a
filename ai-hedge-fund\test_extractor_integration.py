#!/usr/bin/env python
"""
Test script to verify AI Hedge Fund - Extractor App integration
"""
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.credentials_client import get_credentials_client, CredentialsConfig, CredentialsClient
from src.llm.models import get_model, ModelProvider


def test_credentials_client():
    """Test the credentials client functionality"""
    print("🔍 Testing Credentials Client")
    print("-" * 40)
    
    try:
        client = get_credentials_client()
        print(f"✅ Credentials client initialized")
        print(f"   Config: {client.config.extractor_url}")
        
        # Test getting credentials
        print(f"\n📡 Fetching credentials from extractor app...")
        credentials = client.get_credentials()
        
        if credentials:
            print(f"✅ Successfully fetched {len(credentials)} credential(s)")
            
            # Show available providers (without exposing keys)
            providers = []
            for key in credentials.keys():
                if key.endswith('_API_KEY'):
                    provider = key.replace('_API_KEY', '').lower()
                    providers.append(provider)
            
            if providers:
                print(f"   Available providers: {', '.join(providers)}")
            else:
                print(f"   No API keys found")
                
            # Test specific provider access
            print(f"\n🔑 Testing provider access...")
            test_providers = ['openai', 'anthropic', 'groq', 'google', 'deepseek', 'financial_datasets']
            
            for provider in test_providers:
                api_key = client.get_api_key(provider)
                if api_key:
                    # Mask the key for display
                    masked_key = f"{api_key[:8]}...{api_key[-4:]}" if len(api_key) > 12 else "****"
                    print(f"   ✅ {provider}: {masked_key}")
                else:
                    print(f"   ❌ {provider}: Not configured")
            
            # Test OpenAI base URL
            base_url = client.get_openai_base_url()
            if base_url:
                print(f"   🔗 OpenAI Base URL: {base_url}")
            
            return True
            
        else:
            print(f"❌ No credentials fetched")
            return False
            
    except Exception as e:
        print(f"❌ Error testing credentials client: {e}")
        return False


def test_model_initialization():
    """Test model initialization with credentials from extractor"""
    print(f"\n🤖 Testing Model Initialization")
    print("-" * 40)
    
    # Test different providers
    test_cases = [
        (ModelProvider.OPENAI, "gpt-4o"),
        (ModelProvider.ANTHROPIC, "claude-3-5-sonnet-20241022"),
        (ModelProvider.GROQ, "meta-llama/llama-4-scout-17b-16e-instruct"),
        (ModelProvider.DEEPSEEK, "deepseek-reasoner"),
        (ModelProvider.GEMINI, "gemini-2.5-flash-preview-05-20"),
    ]
    
    success_count = 0
    
    for provider, model_name in test_cases:
        try:
            print(f"\n   Testing {provider.value} - {model_name}...")
            model = get_model(model_name, provider)
            
            if model:
                print(f"   ✅ {provider.value}: Model initialized successfully")
                print(f"      Type: {type(model).__name__}")
                success_count += 1
            else:
                print(f"   ❌ {provider.value}: Model initialization returned None")
                
        except ValueError as e:
            if "API key not found" in str(e):
                print(f"   ⚠️  {provider.value}: API key not configured")
            else:
                print(f"   ❌ {provider.value}: {e}")
        except Exception as e:
            print(f"   ❌ {provider.value}: Unexpected error - {e}")
    
    print(f"\n📊 Model initialization results: {success_count}/{len(test_cases)} successful")
    return success_count > 0


def test_financial_api():
    """Test financial data API with credentials from extractor"""
    print(f"\n💰 Testing Financial Data API")
    print("-" * 40)
    
    try:
        from src.tools.api import _get_financial_api_key, get_prices
        
        # Test API key retrieval
        api_key = _get_financial_api_key()
        if api_key:
            masked_key = f"{api_key[:8]}...{api_key[-4:]}" if len(api_key) > 12 else "****"
            print(f"✅ Financial Datasets API key: {masked_key}")
            
            # Test a simple API call
            print(f"   Testing API call with AAPL data...")
            try:
                prices = get_prices("AAPL", "2024-01-01", "2024-01-05")
                if prices:
                    print(f"   ✅ Successfully fetched {len(prices)} price records")
                    return True
                else:
                    print(f"   ⚠️  API call successful but no data returned")
                    return True
            except Exception as e:
                print(f"   ❌ API call failed: {e}")
                return False
        else:
            print(f"❌ Financial Datasets API key not configured")
            return False
            
    except Exception as e:
        print(f"❌ Error testing financial API: {e}")
        return False


def test_fallback_behavior():
    """Test fallback to local environment variables"""
    print(f"\n🔄 Testing Fallback Behavior")
    print("-" * 40)
    
    try:
        # Temporarily modify config to test fallback
        client = get_credentials_client()
        original_url = client.config.extractor_url
        
        # Test with invalid URL to trigger fallback
        client.config.extractor_url = "http://invalid-url:9999"
        client._access_token = None  # Reset auth
        
        print(f"   Testing with invalid extractor URL...")
        credentials = client.get_credentials()
        
        if credentials:
            print(f"✅ Fallback to local environment variables successful")
            print(f"   Found {len(credentials)} local credential(s)")
            
            # Restore original config
            client.config.extractor_url = original_url
            client._access_token = None
            
            return True
        else:
            print(f"⚠️  No local fallback credentials found")
            
            # Restore original config
            client.config.extractor_url = original_url
            client._access_token = None
            
            return False
            
    except Exception as e:
        print(f"❌ Error testing fallback: {e}")
        return False


def main():
    print("🧪 AI Hedge Fund - Extractor Integration Test")
    print("=" * 60)
    
    # Check if .env file exists
    env_path = Path('.env')
    if not env_path.exists():
        print("❌ No .env file found. Please run setup_extractor_integration.py first.")
        return
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded")
    except ImportError:
        print("⚠️  python-dotenv not installed, using system environment")
    
    # Check required configuration
    required_vars = ['EXTRACTOR_URL', 'EXTRACTOR_USERNAME', 'EXTRACTOR_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("   Please run setup_extractor_integration.py to configure.")
        return
    
    print(f"✅ Required configuration found")
    
    # Run tests
    tests = [
        ("Credentials Client", test_credentials_client),
        ("Model Initialization", test_model_initialization),
        ("Financial Data API", test_financial_api),
        ("Fallback Behavior", test_fallback_behavior),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print(f"\n🎉 All tests passed! Integration is working correctly.")
    elif passed > 0:
        print(f"\n⚠️  Some tests failed. Check configuration and credentials.")
    else:
        print(f"\n❌ All tests failed. Please check your setup.")
    
    print(f"\n💡 Tips:")
    print(f"   • Make sure the extractor app is running")
    print(f"   • Verify your credentials in the extractor app web interface")
    print(f"   • Check that API keys are properly configured")
    print(f"   • Review the .env file for correct configuration")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Test cancelled by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
