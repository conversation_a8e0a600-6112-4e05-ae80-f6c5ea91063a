{"name": "resolve", "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "version": "1.22.10", "repository": {"type": "git", "url": "git://github.com/browserify/resolve.git"}, "bin": {"resolve": "./bin/resolve"}, "main": "index.js", "keywords": ["resolve", "require", "node", "module"], "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated && cp node_modules/is-core-module/core.json ./lib/ ||:", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "tests-only": "tape test/*.js", "pretest": "npm run lint", "test": "npm run --silent tests-only", "posttest": "npm run test:multirepo && npx npm@'>= 10.2' audit --production", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "array.prototype.map": "^1.0.7", "copy-dir": "^1.3.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "in-publish": "^2.0.1", "mkdirp": "^0.5.5", "mv": "^2.1.1", "npmignore": "^0.3.1", "object-keys": "^1.1.1", "rimraf": "^2.7.1", "safe-publish-latest": "^2.0.0", "semver": "^6.3.1", "tap": "0.4.13", "tape": "^5.9.0", "tmp": "^0.0.31"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "publishConfig": {"ignore": [".github/workflows", "appveyor.yml", "test/resolver/malformed_package_json", "test/list-exports"]}, "engines": {"node": ">= 0.4"}}