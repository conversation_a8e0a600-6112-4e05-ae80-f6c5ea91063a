from django.conf import settings
from django.contrib import admin, messages
from django.db import models
from django.forms import TextInput, Textarea
from admin_totals.admin import ModelAdminTotals
from django.db.models import Sum, Avg
from django.db.models.functions import Coalesce
from django.http import HttpResponse, HttpResponseRedirect, FileResponse
from django.core.management import call_command
from django.utils.safestring import mark_safe
from django.db.models import Max
from django.urls import path
import dbf
from django.utils.text import slugify

# Import - export libraries for tables
from import_export import resources, fields, widgets
from import_export.admin import ImportExportModelAdmin, ExportMixin
from import_export.forms import ExportForm
from import_export.widgets import ForeignKeyWidget
from django.core.exceptions import MultipleObjectsReturned
from decimal import Decimal, InvalidOperation

from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side
from openpyxl.utils import get_column_letter

from datetime import datetime
import os
import io
import sys
from contextlib import redirect_stdout

import pandas as pd
from simple_history.admin import SimpleHistoryAdmin
from .models import Currency, Bnr, Instrument, Partner_type, Custodian, Account, Partner
from .models import Document_type, Document, Ubo, Operation, Journal, Portfolio, Accounting
from .models import Lock, Deposits


# Register your models here.

@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ('id', 'currency_code', 'currency_name', )
    list_editable = ('currency_name',) 
    list_display_links = ['currency_code']
    ordering = ('currency_code',)
    def __str__(self):
        return self.currency_code


@admin.register(Ubo)
class UboAdmin(admin.ModelAdmin):
    list_display = ('id','ubo_code', 'ubo_name', 'ubo_details')
    list_editable = ('ubo_name', 'ubo_details',) 
    list_display_links = ['ubo_code']


@admin.register(Custodian)
class CustodianAdmin(admin.ModelAdmin):
    list_display = ('id', 'custodian_code', 'custodian_type', 'custodian_name')
    list_editable = ('custodian_name',)
    list_display_links = ['custodian_code']
    ordering = ('custodian_code',)
    change_list_template = "admin/custodian_changelist.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                'uptrz-tdv/',
                self.admin_site.admin_view(self.uptrz_tdv),
                name='uptrz-tdv',
            ),
            path(
                'uptrz-ibkr/',
                self.admin_site.admin_view(self.uptrz_ibkr),
                name='uptrz-ibkr',
            ),
        ]
        return custom_urls + urls

    def handle_command(self, request, command_name):
        output = io.StringIO()
        today = datetime.today().strftime('%Y-%m-%d')
        filename = f"journal_{command_name}_{today}.xlsx"
        filepath = os.path.join(settings.FILE_ROOT, "reports", filename)

        try:
            with redirect_stdout(output):
                if command_name == 'tdv':
                    call_command('uptrz_tdv')
                else:
                    call_command('uptrz_ibkr')
            
            command_output = output.getvalue()
            
            # Create success message with command output
            success_message = f"""
                <div style="margin-bottom: 10px;">{command_name.upper()} journal downloaded successfully</div>
                <div style="font-family: monospace; white-space: pre-wrap; 
                           background-color: #f5f5f5; padding: 10px; border-radius: 4px;">
                    {command_output}
                </div>
            """
            
            self.message_user(
                request,
                mark_safe(success_message),
                messages.SUCCESS
            )

            # Return file download if exists
            if os.path.exists(filepath):
                with open(filepath, 'rb') as fh:
                    response = HttpResponse(fh.read(), content_type='application/vnd.ms-excel')
                    response['Content-Disposition'] = f'attachment; filename="{filename}"'
                    return response

        except Exception as e:
            error_output = output.getvalue()
            error_message = f"""
                <div style="color: #dc3545;">Error running {command_name} download: {str(e)}</div>
                {error_output and f'''
                <div style="font-family: monospace; white-space: pre-wrap; 
                           background-color: #f5f5f5; padding: 10px; border-radius: 4px; 
                           margin-top: 10px;">
                    {error_output}
                </div>
                '''}
            """
            self.message_user(
                request,
                mark_safe(error_message),
                messages.ERROR
            )
        
        finally:
            output.close()

        return HttpResponseRedirect("../")

    def uptrz_tdv(self, request):
        return self.handle_command(request, 'tdv')

    def uptrz_ibkr(self, request):
        return self.handle_command(request, 'ibkr')

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        extra_context['show_uptrz_buttons'] = True
        return super().changelist_view(request, extra_context=extra_context)


class InstrumentResource(resources.ModelResource):
    currency = fields.Field(
        column_name='currency',
        attribute='currency',
        widget=ForeignKeyWidget(Currency, 'currency_code')
    )
    custodian = fields.Field(
        column_name='custodian',
        attribute='custodian',
        widget=ForeignKeyWidget(Custodian, 'custodian_code')
    )

    class Meta:
        model = Instrument
        fields = ('id', 'currency', 'custodian', 'symbol', 'isin', 'name', 'type', 
                'principal', 'depo_start',
                'maturity',  'interest', 
                'bond_issue', 'bond_first_coupon', 'bond_coupon_count', 
                'sector', 'country')
        import_id_fields = ('id',)
        export_order = fields


@admin.register(Instrument)
class InstrumentAdmin(ImportExportModelAdmin):
    resource_class = InstrumentResource

    list_display = (
        'id', 'custodian', 'symbol', 'name', 'currency', 'isin', 'type', 
        'principal', 'face_value', 'depo_start', 
        'maturity',  'interest', 'convention', 'calendar',
        'bond_issue', 'bond_first_coupon', 'bond_coupon_count',
        'sector', 'country', )
    list_editable = ('convention', 'calendar',) 
    list_display_links = ['id']
    formfield_overrides = {
        models.CharField: {'widget': TextInput(attrs={'size':'20'})},
        models.TextField: {'widget': Textarea(attrs={'rows':4, 'cols':40})},
    }
    ordering = ('custodian', 'symbol',)
    list_filter = (
        "type", "custodian", "isin", 
                   )

    def __str__(self):
        return self.symbol


@admin.register(Portfolio)
class PortfolioAdmin(admin.ModelAdmin):
    date_hierarchy = 'date'
    list_display = ('ubo', 'instrument', 'date', 'cost', 'value', 'quantity', )
    list_display_links = None
    list_filter = (
        "instrument__custodian", "instrument", "date", 
                   )
    def __str__(self):
        return self.instrument

@admin.register(Partner_type)
class Partner_typeAdmin(admin.ModelAdmin):
    list_display = ('id', 'partner_type_code', 'journal_code')
    # list_editable = ('journal_code',) 
    list_display_links = ['partner_type_code']



@admin.register(Account)
class AccountAdmin(admin.ModelAdmin):
    list_display = ( 'ubo', 'custodian', 'custodian_detail', 'currency', 'account_code', 'account_name')
    # list_editable = ('account_code', 'account_name',  ) 
    list_display_links = [ 'ubo', ]
    ordering = ('account_code',)


@admin.register(Accounting)
class AccountingAdmin(admin.ModelAdmin):
    list_display = ('id', 'account_code', 'account_name',
                    'has_currency', 
                    'has_custodian_debit', 'has_custodian_credit', 
                    'has_partner_debit', 'has_partner_credit', 
                    'has_symbol', 'has_dot',)
    list_editable = ('has_currency', 'has_custodian_debit', 'has_custodian_credit', 'has_partner_debit', 'has_partner_credit', 'has_symbol', 'has_dot',) 

    list_display_links = []
    ordering = ('account_code',)


@admin.register(Partner)
class PartnerAdmin(admin.ModelAdmin):
    list_display = ('id', 'partner_type', 'partner_code', 'partner_name')
    # list_editable = ('partner_name', 'partner_type') 
    list_display_links = ['partner_code']

    ordering = ('partner_code',)


@admin.register(Operation)
class OperationAdmin(admin.ModelAdmin):
    list_display = ('id', 'operation_code', 'operation_name', 'debit', 'credit', 
                    # 'debit', 'debit_name','credit', 'credit_name', 
                    )
    # list_editable = ('debit', 'credit',  )
    list_display_links = []
    ordering = ('operation_code',)

    def __str__(self):
        return self.operation_code
    


class CompositeForeignKeyWidget(ForeignKeyWidget):
    def __init__(self, model, separator='_', *args, **kwargs):
        self.separator = separator
        super().__init__(model, *args, **kwargs)

    def clean(self, value, row=None, *args, **kwargs):
        if value:
            custodian_code, symbol = value.split(self.separator, 1)
            try:
                return self.model.objects.get(custodian__custodian_code=custodian_code, symbol=symbol)
            except self.model.DoesNotExist:
                return None
        return None

    def render(self, value, obj=None):
        if value:
            return f"{value.custodian.custodian_code}{self.separator}{value.symbol}"
        return ""


class JournalResource(resources.ModelResource):
    """ We create a JournalResource class that inherits from resources.ModelResource. 
    This class defines how the Journal model should be exported and imported.
    We define fields for each foreign key relationship, using ForeignKeyWidget 
    to ensure that both the foreign key and its related value are exported 
    and can be imported.
    """
    ubo = fields.Field(column_name='ubo', attribute='ubo', widget=ForeignKeyWidget(Ubo, 'ubo_code'))
    custodian = fields.Field(column_name='custodian', attribute='custodian', widget=ForeignKeyWidget(Custodian, 'custodian_code'))
    partner = fields.Field(column_name='partner', attribute='partner', widget=ForeignKeyWidget(Partner, 'partner_code'))
    account = fields.Field(column_name='account', attribute='account', widget=ForeignKeyWidget(Account, 'account_code'))
    operation = fields.Field(column_name='operation', attribute='operation', widget=ForeignKeyWidget(Operation, 'operation_code'))
    # instrument = fields.Field(column_name='instrument', attribute='instrument', widget=ForeignKeyWidget(Instrument, 'symbol'))
    instrument = fields.Field(
        column_name='instrument', 
        attribute='instrument', 
        widget=CompositeForeignKeyWidget(Instrument, separator='_')
        )
    
    # Add new field for instrument currency
    instrument_currency = fields.Field(
        column_name='instrument_currency',
        attribute='instrument__currency__currency_code',
        readonly=True
    )
    
    class Meta:
        """ In the Meta class of JournalResource, we specify the model, fields to include, 
        and the import ID field (which is 'id' in this case)."""
        model = Journal
        fields = (
            'id', 'ubo', 'custodian', 'partner', 'account', 'operation', 
            'instrument',  'instrument_currency', 'date', 'transactionid', 'value', 
             'bnr', 'value_ron', 'quantity', 'details', 'storno', 
            )
        import_id_fields = ('id',)
        export_order = fields

    def before_import_row(self, row, **kwargs):
        # Clean up the instrument field if it's empty or just whitespace
        if 'instrument' in row and (row['instrument'] is None or row['instrument'].strip() == ''):
            row['instrument'] = None


@admin.register(Journal)
class JournalAdmin(SimpleHistoryAdmin, ImportExportModelAdmin): # Latest change: SimpleHistoryAdmin added
    """
    We modify the JournalAdmin class to inherit from both SimpleHistoryAdmin and ImportExportModelAdmin.
    This adds both the history tracking and import/export functionality to the admin interface.

    We set the resource_class of JournalAdmin to our custom JournalResource.
    """
    date_hierarchy = 'date'
    resource_class = JournalResource

    list_display = ('id', 'ubo', 'custodian', 'partner', 'account',
                    'transactionid', 'operation',  'instrument', 
                    'date', 
                    'value', 'bnr', 'value_ron',
                    'quantity', 'details', 
                    'storno', 'lock',
                    )
    list_editable = []
    list_display_links = ['id', ]
    list_filter = (
        "lock", "storno", "date", "instrument__type", "custodian", "custodian__custodian_type", "instrument__currency__currency_code", "partner", 
        "operation",  "instrument", "operation__debit", "operation__credit", 
        )
    ordering = ('date', 'custodian', 'transactionid')

    # Actions
    actions = ['jurnal_export', 'jurnal_export_dbf'] 
    





    # Custom template for accrual calculation buttons
    change_list_template = "admin/journal_changelist.html"

    def get_urls(self):
        urls = super().get_urls()
        
        # Get model info for proper URL naming
        info = self.model._meta.app_label, self.model._meta.model_name
        
        custom_urls = [
            path(
                'fix-bnr-rates/',
                self.admin_site.admin_view(self.fix_bnr),
                name='%s_%s_fix-bnr-rates' % info,
            ),
            path(
                'calculate-deposit-accruals/',
                self.admin_site.admin_view(self.calculate_deposit_accruals),
                name='%s_%s_calculate-deposit-accruals' % info,
            ),
            path(
                'calculate-bond-accruals/',
                self.admin_site.admin_view(self.calculate_bond_accruals),
                name='%s_%s_calculate-bond-accruals' % info,
            ),
        ]
        return custom_urls + urls

    def handle_accrual_command(self, request, command_name, display_name):
        """Generic handler for accrual calculation commands"""
        output = io.StringIO()
        today = datetime.today().strftime('%Y-%m-%d')
        
        try:
            with redirect_stdout(output):
                if command_name == 'deposit_accruals':
                    call_command('deposit_accruals')
                elif command_name == 'bond_accruals_ql':
                    call_command('bond_accruals_ql')
            
            command_output = output.getvalue()
            
            # Create success message with command output
            success_message = f"""
                <div style="margin-bottom: 10px;">{display_name} calculated successfully</div>
                <div style="font-family: monospace; white-space: pre-wrap; 
                           background-color: #f5f5f5; padding: 10px; border-radius: 4px;">
                    {command_output}
                </div>
            """
            
            self.message_user(
                request,
                mark_safe(success_message),
                messages.SUCCESS
            )

            # Check for generated files and offer download
            accruals_path = os.path.join(settings.FILE_ROOT, 'accruals')
            if os.path.exists(accruals_path):
                # Find the most recent file matching the command pattern
                if command_name == 'deposit_accruals':
                    pattern = f"accruals_{today}"
                else:  # bond_accruals_ql
                    pattern = f"bond_accruals_{today}"
                
                # Look for Excel files with today's date
                for filename in os.listdir(accruals_path):
                    if pattern in filename and filename.endswith('.xlsx'):
                        filepath = os.path.join(accruals_path, filename)
                        try:
                            with open(filepath, 'rb') as fh:
                                response = HttpResponse(
                                    fh.read(), 
                                    content_type='application/vnd.ms-excel'
                                )
                                response['Content-Disposition'] = f'attachment; filename="{filename}"'
                                return response
                        except FileNotFoundError:
                            pass
                        break

        except Exception as e:
            error_output = output.getvalue()
            error_message = f"""
                <div style="color: #dc3545;">Error calculating {display_name}: {str(e)}</div>
                {error_output and f'''
                <div style="font-family: monospace; white-space: pre-wrap; 
                           background-color: #f5f5f5; padding: 10px; border-radius: 4px; 
                           margin-top: 10px;">
                    {error_output}
                </div>
                '''}
            """
            self.message_user(
                request,
                mark_safe(error_message),
                messages.ERROR
            )
        
        finally:
            output.close()

        return HttpResponseRedirect("../")

    def calculate_deposit_accruals(self, request):
        """Calculate deposit accruals and return result file"""
        return self.handle_accrual_command(request, 'deposit_accruals', 'Deposit Accruals')

    def calculate_bond_accruals(self, request):
        """Calculate bond accruals and return result file"""
        return self.handle_accrual_command(request, 'bond_accruals_ql', 'Bond Accruals')

    def fix_bnr(self, request):
        try:
            output = io.StringIO()
            with redirect_stdout(output):
                call_command('fix_bnr_rates')
            
            command_output = output.getvalue()
            
            success_message = f"""
                <div style="margin-bottom: 10px;">BNR rates errors exported</div>
                <div style="font-family: monospace; white-space: pre-wrap; 
                           background-color: #f5f5f5; padding: 10px; border-radius: 4px;">
                    {command_output}
                </div>
            """
            
            self.message_user(
                request,
                mark_safe(success_message),
                messages.SUCCESS
            )
        except Exception as e:
            error_message = f"""
                <div style="color: #dc3545;">Error exporting BNR rates errors: {str(e)}</div>
            """
            self.message_user(
                request,
                mark_safe(error_message),
                messages.ERROR
            )
            
        return HttpResponseRedirect("../")

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        extra_context['show_fix_bnr_button'] = True
        extra_context['show_accrual_buttons'] = True  # Add this flag
        return super().changelist_view(request, extra_context=extra_context)









    def analitic(self, account, currency_account, currency_instrument, custodian, partner, symbol, credit=True):
        """Calculate analitic account based on account settings"""
        # Get Accounting record for this account
        acc_record = Accounting.objects.filter(account_code=account).first()
        if not acc_record:
            return str(account)

        res = str(account)
        dot = '.' if acc_record.has_dot else ''

        if acc_record.has_currency:
            res += dot + currency_instrument # WAS currency_account

        if credit:
            if acc_record.has_custodian_credit:
                res += dot + custodian
            if acc_record.has_partner_credit:
                res += dot + partner
        else:
            if acc_record.has_custodian_debit:
                res += dot + custodian
            if acc_record.has_partner_debit:
                res += dot + partner            
        
        if acc_record.has_symbol:
            res += dot + symbol

        # Special case handling
        if (str(account)=='461') and credit and (partner=='BBG'):
            res = '461.BBG'
            
        return res


    def jurnal_export(self, request, queryset):
        wb = Workbook()
        ws = wb.active
        ws.title = "Jurnal"

        # Define headers
        headers = [
            'Firma', 'Banca', 'Cont', 'Operatie', 'Contraparte', 'Instrument',
            'Valoare', 'Valoare Deviza', 'Cantitate', 'Storno', '.',
            'Nr.inreg.','Tip inregistrare', 'Jurnal', 'Data', 'Data scadenta', 'Numar document', 
            'Cod tip factura', 
            'Cont debit simbol', 'Cont debit titlu',
            'Metoda de plata SAF-T', 'Mecanism de plata SAFT-T', 'Tip Taxa SAF_T', 'Cod TAXA SAF_T',           
            'Cont credit simbol', 'Cont credit titlu', 
            'Metoda de plata SAF-T', 'Mecanism de plata SAFT-T', 'Tip Taxa SAF_T', 'Cod TAXA SAF_T',  
            'Explicatie', 'Valoare',
            'Cod Partener', 'Partener CIF', 'Partener Nume', 'Partener Rezidenta', 'Partener Judet', 
            'Partener Cont', 'Angajat CNP', 'Angajat Nume', 'Angajat Cont', 'Optiune TVA', 
            'Cota TVA', 'Cod TVA SAF-T',
            'Moneda', 'Curs BNR', 
            'Valoare deviza', 
            'Stornare - Nr. inreg.', 'Incasari/plati', 'Diferente curs', 'TVA la incasare', 'Colectare/Deducere TVA', 
            'Efect de incasat/platit', 'Banca efect', 'Centre de cost', 'Informatii export', 'Punct de lucru', 
            'Deductibilitate', 'Reevaluare', 'Factura simplificata', 'Borderou de achizitie', 'arnet prod. Agricole', 
            'Contract', 'Document stornat',    
        ]

        # Style for headers
        header_style = Font(bold=True)
        header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # Write headers with styling
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_style
            cell.fill = header_fill
            ws.column_dimensions[get_column_letter(col)].width = max(len(str(header)) + 2, 12)

        # Write data from queryset
        for row_num, record in enumerate(queryset.values(
            'id',
            'ubo__ubo_code',
            'custodian__custodian_code',
            'account__account_code',
            'account__custodian_detail',
            'operation__operation_code',
            'partner__partner_code',
            'instrument__symbol',
            'value',
            'value_ron',
            'quantity',
            'storno',

            'custodian__custodian_type__partner_type_code',
            'custodian__custodian_type__journal_code',
            'date',
            'transactionid',
            'operation__debit__account_code',
            'operation__debit__account_name',
            'operation__credit__account_code',
            'operation__credit__account_name',
            'details',
            'instrument__currency__currency_code', # replaced
            'account__currency__currency_code',
            'bnr'
            ).order_by('date', 'custodian__custodian_code', 'transactionid'), 2):
            
            # Calculate analitic accounts
            custodian = record['custodian__custodian_code'] + (record['account__custodian_detail'] or '')
            currency_instrument = record['instrument__currency__currency_code']
            currency_account = record['account__currency__currency_code']
            partner = record['partner__partner_code']
            symbol = record['instrument__symbol']

            debit_analitic = self.analitic(record['operation__debit__account_code'], currency_account, currency_instrument, custodian, partner, symbol, credit=False)
            credit_analitic = self.analitic(record['operation__credit__account_code'], currency_account, currency_instrument,  custodian, partner, symbol, credit=True)

            row_data = [
                record['ubo__ubo_code'],
                record['custodian__custodian_code'],
                record['account__account_code'],
                record['operation__operation_code'],
                record['partner__partner_code'],
                record['instrument__symbol'],
                record['value_ron'],
                record['value'],
                record['quantity'],
                'x' if record['storno'] else '',
                '.',

                # row_num - 1,
                record['id'],

                record['custodian__custodian_type__partner_type_code'],
                record['custodian__custodian_type__journal_code'],
                record['date'].strftime('%Y%m%d'),
                record['date'].strftime('%Y%m%d'),
                record['transactionid'],
                '',  # Cod tip factura

                debit_analitic,  # Using calculated debit_analitic
                record['operation__debit__account_name'],
                '', '', '', '',

                credit_analitic,  # Using calculated credit_analitic
                record['operation__credit__account_name'],
                '', '', '', '',
                
                record['details'],
                abs(record['value_ron']) if not record['storno'] else -abs(record['value_ron']),  ### ATENTIE
                '', '', '', '', '', '', '', '', '', 
                'Neimpozabile', '0.00', '',

                # record['instrument__currency__currency_code'], 
                currency_account,
                record['bnr'],
                abs(record['value']) if not record['storno'] else -abs(record['value']), ### ATENTIE

                '', '', '', '0', '',
                '', '', '', '********-********-CielStd_ASV', 'Sediu', 
                '', '', '0', '0', '0', 
                '0', '0', 
            ]
            
            for col_num, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_num, column=col_num, value=value)
                # Format numbers to have 2 decimal places
                if isinstance(value, (float, int)) and col_num in [7, 8, 9, 22, 24, 25]:
                    cell.number_format = '#,##0.00'

        # Create response
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=Jurnal.xlsx'
        
        wb.save(response)
        return response

    jurnal_export.short_description = "Export selected records to Excel"

    # END NEW
    
    # dbf export feature
    def jurnal_export_dbf(self, request, queryset):
        queryset = queryset.order_by("date")
        helper_mapping = pd.read_excel(os.path.join(settings.BASE_DIR, 'port/helper_files/helper_mapping.xlsx'), sheet_name='helper_mapping')
        first_date = queryset.first().date.strftime("%d-%m-%Y")
        last_date = queryset.last().date.strftime("%d-%m-%Y")
        filename = f"NC_{first_date}_{last_date}"

        table_path = f"/tmp/{slugify(filename)}.dbf"
        table = dbf.Table(
            table_path,
            """
                NDP C(16);
                CONT_D C(20);
                CONT_C C(20);
                SUMA N(15,2);
                COD_VALUTA C(3);
                CURS N(15,4);
                SUMA_VAL N(14,2);
                DATA D;
                EXPLICATIE C(48);
                GRUPA C(16);
                NR_DOC C(16)
            """
        )
        table.open(mode=dbf.READ_WRITE)

        for record in queryset:
            debit = self.analitic(
                record.operation.debit.account_code,
                record.account.currency.currency_code,
                record.instrument.currency.currency_code,
                record.custodian.custodian_code + (record.account.custodian_detail or ''),
                record.partner.partner_code,
                record.instrument.symbol,
                credit=False
            )
            credit = self.analitic(
                record.operation.credit.account_code,
                record.account.currency.currency_code,
                record.instrument.currency.currency_code,
                record.custodian.custodian_code + (record.account.custodian_detail or ''),
                record.partner.partner_code,
                record.instrument.symbol,
                credit=True
            )
            debit_account = helper_mapping.loc[helper_mapping['conturi django'] == debit, 'conturi saga'].values
            credit_account = helper_mapping.loc[helper_mapping['conturi django'] == credit, 'conturi saga'].values
            if credit_account.size > 0:
                credit_account = credit_account[0]
            else:
                # provizoriu
                print(f"Credit account not found for {credit}")
                credit_account = "473"
            if debit_account.size > 0:
                debit_account = debit_account[0]
            else:
                # provizoriu
                print(f"Debit account not found for {debit}")
                debit_account = "473"
            record: Journal
            try:
                suma = record.value_ron
                suma = float(suma)
            except Exception as e:
                print(f"Error for converting suma: {record.value_ron}")
                suma = 0.0
            try:
                curs = record.bnr
                curs = float(curs)
            except Exception as e:
                print(f"Error for converting curs: {record.curs}")
                curs = 0.0
            table.append((
                str(record.id),
                str(debit_account),
                str(credit_account),
                abs(record.value_ron) if not record.storno else -abs(record.value_ron),
                str(record.account.currency.currency_code),
                curs,
                abs(record.value) if not record.storno else -abs(record.value),
                # record.date.strftime('%Y%m%d'),
                record.date,
                str(record.operation.operation_code),
                "",
                str(record.id),
            ))

        table.close()

        with open(table_path, 'rb') as f:
            response = HttpResponse(f.read(), content_type='application/octet-stream')
            response['Content-Disposition'] = f'attachment; filename={filename}.dbf'

        return response

    jurnal_export_dbf.short_description = "Export selected records to DBF"

    # NEW 2
    history_list_display = ['changed_fields']  # Optional: show changed fields in history list

    def changed_fields(self, obj):
        if obj.prev_record:
            changed_fields = []
            for field in self.model._meta.fields:
                if getattr(obj, field.name) != getattr(obj.prev_record, field.name):
                    changed_fields.append(field.name)
            return ", ".join(changed_fields)
        return None
    changed_fields.short_description = "Changed fields"

    # END NEW 2


    def get_queryset(self, request):
        qs = super(JournalAdmin, self).get_queryset(request)
        # Here's where we specify what to filter our queryset by.
        # qs.filter(lock=False) 
        return qs






@admin.register(Bnr)
class BnrAdmin(admin.ModelAdmin):
    list_display = ('currency_code', 'date', 'value', 'value_exact',)
    list_display_links = ['currency_code']
    list_filter = ["date", "currency_code", ]
    ordering = ('-date',)
    actions = ['export_to_excel',]

    # NEW
    change_list_template = "admin/bnr_changelist.html"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                'update-bnr/',
                self.admin_site.admin_view(self.update_bnr),
                name='update-bnr',
            ),
        ]
        return custom_urls + urls

    def update_bnr(self, request):
        # Create a string buffer to capture output
        output = io.StringIO()
        try:
            # Redirect stdout to our string buffer
            with redirect_stdout(output):
                call_command('curs_bnr')
            
            # Get the captured output
            command_output = output.getvalue()
            
            # Format the success message with command output
            success_message = """
                <div style="margin-bottom: 10px;">BNR exchange rates updated successfully</div>
                <div style="font-family: monospace; white-space: pre-wrap; 
                           background-color: #f5f5f5; padding: 10px; border-radius: 4px;">
                    {}
                </div>
            """.format(command_output)
            
            self.message_user(
                request,
                mark_safe(success_message),  # mark_safe because we want HTML formatting
                messages.SUCCESS
            )
        except Exception as e:
            error_output = output.getvalue()
            error_message = f"""
                <div style="color: #dc3545;">Error updating BNR rates: {str(e)}</div>
                {error_output and f'''
                <div style="font-family: monospace; white-space: pre-wrap; 
                           background-color: #f5f5f5; padding: 10px; border-radius: 4px; 
                           margin-top: 10px;">
                    {error_output}
                </div>
                '''}
            """
            self.message_user(
                request,
                mark_safe(error_message),
                messages.ERROR
            )
        finally:
            output.close()
            
        return HttpResponseRedirect("../")

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        extra_context['show_bnr_button'] = True
        return super().changelist_view(request, extra_context=extra_context)

    # /NEW

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.filter(currency_code__currency_code__in=['EUR', 'USD', 'MXN', 'GBP', ])

    def export_to_excel(self, request, queryset):
    
        wb = Workbook()
        ws = wb.active
        ws.title = "BNR Exchange Rates"

        # Define headers
        headers = [
            'Currency',
            'Date',
            'Value',
            'Value Exact',
        ]

        # Style for headers
        header_font = Font(bold=True)
        header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

        # Write headers with styling
        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num, value=header)
            cell.font = header_font
            cell.fill = header_fill
            # Set column width based on header length
            ws.column_dimensions[get_column_letter(col_num)].width = max(len(str(header)) + 2, 12)

        # Write data
        for row_num, record in enumerate(queryset.values_list(
            'currency_code__currency_code',
            'date',
            'value',
            'value_exact'
        ).order_by('-date', 'currency_code'), 2):
            for col_num, value in enumerate(record, 1):
                cell = ws.cell(row=row_num, column=col_num, value=value)
                # Format numbers to have 4 decimal places
                if isinstance(value, (float, int)) and col_num in [3, 4]:
                    cell.number_format = '#,##0.0000'
                # Format dates
                elif isinstance(value, datetime):
                    cell.number_format = 'YYYY-MM-DD'

        # Create response
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename=BNR_Rates_{datetime.now().strftime("%Y%m%d")}.xlsx'
        
        wb.save(response)
        return response

    export_to_excel.short_description = "Export selected records to Excel"


admin.site.register(Document_type)
admin.site.register(Document)


@admin.register(Lock)
class LockAdmin(admin.ModelAdmin):
    list_display = ('lock_date', )
    list_display_links = ['lock_date']
    list_filter = ["lock_date", ]
    ordering = ('-lock_date',)
    change_list_template = "admin/lock_changelist.html"  # Custom template for button

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                'lock-journals/',
                self.admin_site.admin_view(self.lock_journals),
                name='lock-journals',
            ),
        ]
        return custom_urls + urls

    def lock_journals(self, request):
        try:
            # Get max lock date
            max_lock_date = Lock.objects.aggregate(Max('lock_date'))['lock_date__max']
            
            if not max_lock_date:
                self.message_user(
                    request,
                    "No lock dates found. Please create a lock date first.",
                    messages.ERROR
                )
                return HttpResponseRedirect("../")

            # Lock all journal entries with date <= max_lock_date
            updated_count = Journal.objects.filter(
                date__lte=max_lock_date,
                lock=False
            ).update(lock=True)

            # Success message with count of updated records
            success_message = f"Successfully locked {updated_count} journal entries up to {max_lock_date}"
            self.message_user(request, success_message, messages.SUCCESS)

        except Exception as e:
            self.message_user(
                request,
                f"Error locking journals: {str(e)}",
                messages.ERROR
            )

        return HttpResponseRedirect("../")

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        extra_context['show_lock_button'] = True
        return super().changelist_view(request, extra_context=extra_context)



class DepositsResource(resources.ModelResource):
    # Fields for export
    custodian = fields.Field(
        column_name='custodian',
        attribute='deposit__custodian__custodian_code',
        readonly=True
    )
    symbol = fields.Field(
        column_name='symbol',
        attribute='deposit__symbol',
        readonly=True
    )
    currency = fields.Field(
        column_name='currency',
        attribute='deposit__currency__currency_code',
        readonly=True
    )
    
    # Add deposit as a proper foreign key field
    deposit = fields.Field(
        column_name='deposit',
        attribute='deposit',
        widget=ForeignKeyWidget(Instrument, 'symbol')
    )

    class Meta:
        model = Deposits
        fields = (
            'custodian', 'symbol', 'currency',  # For export only
            'deposit', 'principal', 'interest_rate', 
            'start', 'maturity', 'convention',
            'interest_amount', 'interest_calculated', 'check_actual_vs_calc', 
            'new_deposit', 'liquidated', 'details'
        )
        import_id_fields = ('deposit', 'maturity')
        export_order = fields

    def before_import_row(self, row, **kwargs):
        """
        Pre-process import data to properly map custodian and deposit fields
        """
        if 'custodian' in row and 'deposit' in row:
            try:
                # Look up the instrument
                custodian = row['custodian'].strip()
                symbol = row['deposit'].strip()
                
                # Find the matching instrument
                instrument = Instrument.objects.get(
                    custodian__custodian_code=custodian,
                    symbol=symbol
                )
                
                # Store just the symbol value for the ForeignKeyWidget to use
                row['deposit'] = symbol
                
            except Instrument.DoesNotExist:
                raise ValueError(
                    f"No Instrument found for custodian={custodian!r} and symbol={symbol!r}"
                )
            except Instrument.MultipleObjectsReturned:
                raise ValueError(
                    f"Multiple Instruments found for custodian={custodian!r} and symbol={symbol!r}"
                )

    def get_instance(self, instance_loader, row):
        """
        Handle unique constraint on deposit and maturity
        """
        try:
            params = {}
            if 'deposit' in row:
                # Look up instrument using symbol
                symbol = row['deposit'].strip()
                custodian = row['custodian'].strip()
                params['deposit'] = Instrument.objects.get(
                    custodian__custodian_code=custodian,
                    symbol=symbol
                )
                
            if 'maturity' in row:
                params['maturity'] = row['maturity']
            
            if params:
                return self.Meta.model.objects.get(**params)
        except self.Meta.model.DoesNotExist:
            return None
        

@admin.register(Deposits)
class DepositsAdmin(ImportExportModelAdmin):
    resource_class = DepositsResource
    
    list_display = (
        'get_custodian',
        'get_symbol',
        'get_currency',
        'principal', 
        'interest_rate',
        'start', 
        'maturity', 
        'convention',
        'interest_amount', 
        'interest_calculated', 
        'check_actual_vs_calc',
        'new_deposit', 
        'liquidated'
    )
    list_filter = ('deposit__custodian', 'convention', 'new_deposit', 'liquidated')
    search_fields = ('deposit__symbol', 'details')
    ordering = ('deposit__custodian', 'deposit__symbol', 'maturity')
    
    change_list_template = "admin/deposits_changelist.html"

    def get_custodian(self, obj):
        return obj.deposit.custodian.custodian_code
    get_custodian.short_description = 'Custodian'
    get_custodian.admin_order_field = 'deposit__custodian__custodian_code'

    def get_symbol(self, obj):
        return obj.deposit.symbol
    get_symbol.short_description = 'Symbol'
    get_symbol.admin_order_field = 'deposit__symbol'

    def get_currency(self, obj):
        return obj.deposit.currency.currency_code
    get_currency.short_description = 'Currency'
    get_currency.admin_order_field = 'deposit__currency__currency_code'

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                'deposit-accruals/',
                self.admin_site.admin_view(self.deposit_accruals),
                name='deposit-accruals',
            ),
        ]
        return custom_urls + urls

    def deposit_accruals(self, request):
        try:
            output = io.StringIO()
            with redirect_stdout(output):
                call_command('deposit_accruals')
            
            command_output = output.getvalue()
            success_message = f"""
                <div style="margin-bottom: 10px;">Deposit accruals calculated successfully</div>
                <div style="font-family: monospace; white-space: pre-wrap; 
                           background-color: #f5f5f5; padding: 10px; border-radius: 4px;">
                    {command_output}
                </div>
            """
            
            self.message_user(
                request,
                mark_safe(success_message),
                messages.SUCCESS
            )
        except Exception as e:
            error_message = f"""
                <div style="color: #dc3545;">Error calculating accruals: {str(e)}</div>
            """
            self.message_user(
                request,
                mark_safe(error_message),
                messages.ERROR
            )
            
        return HttpResponseRedirect("../")

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        extra_context['show_calculate_button'] = True
        return super().changelist_view(request, extra_context=extra_context)