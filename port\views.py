from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.db.models.functions import Round
from django.db.models import OuterRef, Subquery, F
from django_tables2 import SingleTableView

from django.shortcuts import redirect
from django.core.management import call_command
from django.contrib import messages

import pandas as pd


from .models import Portfolio, Journal, Bnr, Partner_type, Partner, Custodian, Operation, Accounting
from .tables import PortTable




# Create your views here.

def index(request):
    return render(request, 'port/index.html')


@login_required
def portfolio(request):
    custodians = Portfolio.objects.values('instrument__custodian')\
        .distinct().values_list('instrument__custodian', flat=True)
    
    res = Portfolio.objects.none()
 
    for c in custodians:
        maxdate = Portfolio.objects.filter(instrument__custodian=c)\
            .latest('date').date
        res = res | Portfolio.objects.filter(instrument__custodian=c, date=maxdate)

    return render(request, 'port/portfolio.html', {'portfolio': res})


@login_required
def port(request):
    custodians = Portfolio.objects.values('instrument__custodian')\
        .distinct().values_list('instrument__custodian', flat=True)
    
    res = Portfolio.objects.none()
 
    for c in custodians:
        maxdate = Portfolio.objects.filter(instrument__custodian=c)\
            .latest('date').date
        res = res | Portfolio.objects.filter(instrument__custodian=c, date=maxdate)

    return render(request, 'port/port.html', {'portfolio': res})


@login_required
def jurnal(request):

    # Map Accounting table
    mx = pd.DataFrame(list(Accounting.objects.all().values()))

    def analitic(account, currency, custodian, partner, symbol, credit=True):
        res = str(account)
        has_currency = mx[mx['account_code']==account]['has_currency'].to_list()[0]
        has_custodian_credit = mx[mx['account_code']==account]['has_custodian_credit'].to_list()[0]
        has_custodian_debit = mx[mx['account_code']==account]['has_custodian_debit'].to_list()[0]
        has_partner_credit = mx[mx['account_code']==account]['has_partner_credit'].to_list()[0]
        has_partner_debit = mx[mx['account_code']==account]['has_partner_debit'].to_list()[0]
        has_dot = mx[mx['account_code']==account]['has_dot'].to_list()[0]

        dot = '.' if has_dot else ''
        custodian_detail = mx[mx['account_code']==account]['has_partner_debit'].to_list()[0]

        if has_currency:
            res += dot + currency

        if credit:
            if has_custodian_credit:
                res += dot + custodian
            if has_partner_credit:
                res += dot + partner
        else:
            if has_custodian_debit:
                res += dot + custodian
            if has_partner_debit:
                res += dot + partner            
        
        has_symbol = mx[mx['account_code']==account]['has_symbol'].to_list()[0]
        if has_symbol:
            res += dot + symbol

        # Exceptions
        if (str(account)=='461') and credit and(partner=='BBG'):
            res = '461.BBG'
            
        return res


    jurnal = Journal.objects.values(
            
            'custodian__custodian_type__partner_type_code',
            'custodian__custodian_type__journal_code',

            'ubo__ubo_code', 
            'custodian__custodian_code',
            
            'account__account_code',
            'account__custodian_detail',
            'operation__operation_code', 
            'partner__partner_code', 
            'custodian__custodian_type', 
            'instrument__symbol', 
            'instrument__currency__currency_code', 

            'date',
            'value',
            'bnr',
            'value_ron',
            # 'unit_cost',
            # 'profit',
            
            'quantity',
            'details',
            'transactionid', 

            'operation__credit__account_code',
            'operation__credit__account_name',
            'operation__debit__account_code',
            'operation__debit__account_name',
            'storno',

        ).order_by('date', 'custodian', 'transactionid')
    
    jurnal_res = []
    for row in jurnal:
        currency = row['instrument__currency__currency_code']
        custodian = row['custodian__custodian_code'] + row['account__custodian_detail']
        partner = row['partner__partner_code']
        symbol = row['instrument__symbol']

        row['debit_analitic']= analitic(row['operation__debit__account_code'], currency, custodian, partner, symbol, credit=False)
        row['credit_analitic']= analitic(row['operation__credit__account_code'], currency, custodian, partner, symbol, credit=True)

        if row['storno']:
            row['value_abs'] = - abs(row['value']) 
            row['value_ron_abs'] = - abs(row['value_ron'])
        else:
            row['value_abs'] = abs(row['value'])
            row['value_ron_abs'] = abs(row['value_ron'])


        jurnal_res += row

    return render(request, 'port/jurnal.html', {'jurnal': jurnal})


@login_required
def map_operations(request):
    op = Operation.objects.\
        values(
            'operation_code',
            'operation_name',
            'debit__account_code',
            'debit__account_name',
            'credit__account_code', 
            'credit__account_name',
        ).order_by('operation_code',)
    
    return render(request, 'port/map_operations.html', {'op': op})