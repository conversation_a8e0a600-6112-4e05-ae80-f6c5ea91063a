version: "3.8"

services:
  web:
    build: .
    command: >
      sh -c "python manage.py makemigrations --noinput &&
             python manage.py migrate --noinput &&
             python manage.py createsuperuser --noinput || true &&
             python -m debugpy --listen 0.0.0.0:5678 manage.py runserver 0.0.0.0:8000 --nothreading --noreload"
    volumes:
      - .:/app
      - django_logs:/app/.logs
      - ./files:/app/files/ibkr
    ports:
      - "8002:8000" # 8002 port for test app
      - "5678:5678"  # Debug port
    env_file:
      - .env
    environment:
      - ENVIRONMENT=development
      - DEBUG=${DEBUG}
      - SECRET_KEY=${SECRET_KEY}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_HOST=${DB_HOST}
      - REDIS_HOST=${REDIS_HOST}
    depends_on:
      - postgres
      - redis
    networks:
      - app-network

  frontend:
    image: nginx:alpine
    volumes:
      - ./frontend:/usr/share/nginx/html
      - ./frontend/static:/usr/share/nginx/html/static
      - ./nginx/frontend.conf:/etc/nginx/conf.d/default.conf
    ports:
      - "8082:8080" # 8082 port for test app
    depends_on:
      - web
    networks:
      - app-network

  celery_worker:
    build: .
    command: >
      sh -c "python -m debugpy --listen 0.0.0.0:5679 -m celery -A nch worker -l info"
    volumes:
      - .:/app
      - django_logs:/app/.logs
      - ./files:/app/files/ibkr
    ports:
      - "5679:5679"  # Debug port for celery worker
    env_file:
      - .env
    environment:
      - DEBUG=${DEBUG}
      - SECRET_KEY=${SECRET_KEY}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_HOST=${DB_HOST}
      - REDIS_HOST=${REDIS_HOST}
    depends_on:
      - web
      - redis
      - postgres
    networks:
      - app-network

  celery_beat:
    build: .
    command: celery -A nch beat -l info
    volumes:
      - .:/app
      - django_logs:/app/.logs
    env_file:
      - .env
    environment:
      - DEBUG=${DEBUG}
      - SECRET_KEY=${SECRET_KEY}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_HOST=${DB_HOST}
      - REDIS_HOST=${REDIS_HOST}
    depends_on:
      - web
      - redis
      - postgres
    networks:
      - app-network

  flower:
    build: .
    command: celery -A nch flower --port=5555
    volumes:
      - .:/app
      - django_logs:/app/.logs
    ports:
      - "5557:5555" # 5557 port for test app
    environment:
      - DEBUG=${DEBUG}
      - SECRET_KEY=${SECRET_KEY}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_HOST=${DB_HOST}
      - REDIS_HOST=${REDIS_HOST}
      - CELERY_BROKER_URL=redis://redis:6379/0
      - FLOWER_BASIC_AUTH=${FLOWER_USER}:${FLOWER_PASSWORD}
    depends_on:
      - redis
      - celery_worker
    env_file:
      - .env
    networks:
      - app-network

  postgres:
    profiles: ["development"]
    build:
      context: .
      dockerfile: Dockerfile.postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    ports:
      - "5434:5432" # 5434 port for test app
    env_file:
      - .env
    networks:
      - app-network

  redis:
    image: redis:7.2
    ports:
      - "6381:6379" # 6381 port for test app
    volumes:
      - redis_data:/data
    env_file:
      - .env
    networks:
      - app-network

  ai-hedge-fund-backend:
    build:
      context: ./ai-hedge-fund
      dockerfile: docker/Dockerfile
    command: >
      sh -c "cd /app &&
             poetry run uvicorn app.backend.main:app --reload --host 0.0.0.0 --port 8000"
    volumes:
      - ./ai-hedge-fund:/app
      - ai_hedge_fund_logs:/app/.logs
    ports:
      - "8003:8000"  # AI hedge fund backend API
    env_file:
      - .env
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
      - DB_HOST=${DB_HOST}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
    depends_on:
      - postgres
      - redis
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:
  django_logs:
  ibkr_data:
  ai_hedge_fund_logs:

networks:
  app-network:
    driver: bridge


