<div class="container mt-5">
    <h2 class="mb-4">
        <i class="fas fa-robot me-2"></i>AI Support & Hedge Fund
    </h2>

    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs mb-4" id="aiTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="hedge-fund-tab" data-bs-toggle="tab"
                    data-bs-target="#hedge-fund" type="button" role="tab">
                <i class="fas fa-chart-line me-2"></i>AI Hedge Fund
            </button>
        </li>
        <!-- <li class="nav-item" role="presentation">
            <button class="nav-link" id="support-tab" data-bs-toggle="tab"
                    data-bs-target="#support" type="button" role="tab">
                <i class="fas fa-headset me-2"></i>AI Support
            </button>
        </li> -->
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="aiTabsContent">
        <!-- AI Hedge Fund Tab -->
        <div class="tab-pane fade show active" id="hedge-fund" role="tabpanel">
            <!-- Configuration Panel -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Configuration</h5>
                        </div>
                        <div class="card-body">
                            <form id="hedgeFundForm">
                                <div class="row">
                                    <!-- Tickers -->
                                    <div class="col-md-6 mb-3">
                                        <label for="tickers" class="form-label">Stock Tickers</label>
                                        <input type="text" class="form-control" id="tickers"
                                               placeholder="AAPL,GOOGL,MSFT,TSLA"
                                               value="AAPL,GOOGL,MSFT,TSLA">
                                        <div class="form-text">Comma-separated list of stock symbols</div>
                                    </div>

                                    <!-- Initial Cash -->
                                    <div class="col-md-6 mb-3">
                                        <label for="initialCash" class="form-label">Initial Cash ($)</label>
                                        <input type="number" class="form-control" id="initialCash"
                                               value="100000" min="1000" step="1000">
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- Start Date -->
                                    <div class="col-md-6 mb-3">
                                        <label for="startDate" class="form-label">Start Date</label>
                                        <input type="date" class="form-control" id="startDate">
                                    </div>

                                    <!-- End Date -->
                                    <div class="col-md-6 mb-3">
                                        <label for="endDate" class="form-label">End Date</label>
                                        <input type="date" class="form-control" id="endDate">
                                    </div>
                                </div>

                                <div class="row">
                                    <!-- Model Provider -->
                                    <div class="col-md-6 mb-3">
                                        <label for="modelProvider" class="form-label">AI Model Provider</label>
                                        <select class="form-select" id="modelProvider">
                                            <option value="OPENAI">OpenAI</option>
                                            <option value="ANTHROPIC">Anthropic</option>
                                            <option value="GROQ">Groq</option>
                                        </select>
                                    </div>

                                    <!-- Model Name -->
                                    <div class="col-md-6 mb-3">
                                        <label for="modelName" class="form-label">Model Name</label>
                                        <select class="form-select" id="modelName">
                                            <option value="gpt-4">GPT-4</option>
                                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Agents Selection -->
                                <div class="mb-3">
                                    <label class="form-label">AI Agents</label>
                                    <div id="agentsContainer" class="border rounded p-3">
                                        <div class="text-center text-muted">
                                            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                            Loading available agents...
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-success" id="runAnalysisBtn">
                                        <i class="fas fa-play me-2"></i>Run Analysis
                                    </button>
                                    <button type="button" class="btn btn-secondary" id="stopAnalysisBtn" disabled>
                                        <i class="fas fa-stop me-2"></i>Stop Analysis
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Status Panel -->
                <div class="col-lg-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Status</h5>
                        </div>
                        <div class="card-body">
                            <div id="statusContainer">
                                <div class="text-center text-muted">
                                    <i class="fas fa-clock fa-2x mb-2"></i>
                                    <p>Ready to run analysis</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progress Panel -->
            <div class="card shadow-sm mb-4" id="progressPanel" style="display: none;">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Analysis Progress</h5>
                </div>
                <div class="card-body">
                    <div id="progressContainer">
                        <!-- Progress updates will be displayed here -->
                    </div>
                </div>
            </div>

            <!-- Results Panel -->
            <div class="card shadow-sm" id="resultsPanel" style="display: none;">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Analysis Results</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <h6>Investment Decisions</h6>
                            <div id="decisionsContainer" class="border rounded p-3 mb-3">
                                <!-- Investment decisions will be displayed here -->
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <h6>Analyst Signals</h6>
                            <div id="signalsContainer" class="border rounded p-3 mb-3">
                                <!-- Analyst signals will be displayed here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Support Tab -->
        <!-- <div class="tab-pane fade" id="support" role="tabpanel">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-headset me-2"></i>AI Support Assistant</h5>
                </div>
                <div class="card-body">
                    <div class="text-center text-muted">
                        <i class="fas fa-tools fa-3x mb-3"></i>
                        <h5>AI Support Coming Soon</h5>
                        <p>Advanced AI-powered support features will be available here.</p>
                    </div>
                </div>
            </div>
        </div> -->
    </div>
</div>

<script type="module">
    // Import and initialize AI Hedge Fund functionality
    import { AI_HEDGE_FUND_URL } from '/js/constants.js';

    class AIHedgeFund {
        constructor() {
            this.eventSource = null;
            this.isRunning = false;
            this.agents = [];
            this.models = [];
            this.init();
        }

        async init() {
            this.setupEventListeners();
            this.setDefaultDates();
            await this.loadAgents();
            await this.loadModels();
        }

        setupEventListeners() {
            const form = document.getElementById('hedgeFundForm');
            const stopBtn = document.getElementById('stopAnalysisBtn');
            const modelProvider = document.getElementById('modelProvider');

            if (form) form.addEventListener('submit', (e) => this.handleSubmit(e));
            if (stopBtn) stopBtn.addEventListener('click', () => this.stopAnalysis());
            if (modelProvider) modelProvider.addEventListener('change', () => this.updateModelOptions());
        }

        setDefaultDates() {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 90); // 90 days ago

            const endDateInput = document.getElementById('endDate');
            const startDateInput = document.getElementById('startDate');

            if (endDateInput) endDateInput.value = endDate.toISOString().split('T')[0];
            if (startDateInput) startDateInput.value = startDate.toISOString().split('T')[0];
        }

        async loadAgents() {
            try {
                const response = await fetch(`${AI_HEDGE_FUND_URL}/hedge-fund/agents`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                this.agents = data.agents || [];
                this.renderAgents();
            } catch (error) {
                console.error('Failed to load agents:', error);
                this.showError('Failed to load available agents. Make sure AI Hedge Fund backend is running.');
            }
        }

        async loadModels() {
            try {
                const response = await fetch(`${AI_HEDGE_FUND_URL}/hedge-fund/language-models`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                this.models = data.models || [];
                this.updateModelOptions();
            } catch (error) {
                console.error('Failed to load models:', error);
                this.showError('Failed to load available models. Make sure AI Hedge Fund backend is running.');
            }
        }

        renderAgents() {
            const container = document.getElementById('agentsContainer');
            if (!container) return;

            if (this.agents.length === 0) {
                container.innerHTML = '<div class="text-muted">No agents available</div>';
                return;
            }

            const agentsHtml = this.agents.map(agent => `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${agent.id}"
                           id="agent_${agent.id}" checked>
                    <label class="form-check-label" for="agent_${agent.id}">
                        <strong>${agent.name}</strong>
                        <br><small class="text-muted">${agent.description}</small>
                    </label>
                </div>
            `).join('');

            container.innerHTML = agentsHtml;
        }

        updateModelOptions() {
            const provider = document.getElementById('modelProvider')?.value;
            const modelSelect = document.getElementById('modelName');
            if (!provider || !modelSelect) return;

            // Filter models by provider
            const providerModels = this.models.filter(model =>
                model.provider.toUpperCase() === provider
            );

            modelSelect.innerHTML = providerModels.map(model =>
                `<option value="${model.name}">${model.display_name || model.name}</option>`
            ).join('');
        }

        async handleSubmit(e) {
            e.preventDefault();

            if (this.isRunning) {
                this.showError('Analysis is already running');
                return;
            }

            const formData = this.getFormData();
            if (!this.validateFormData(formData)) {
                return;
            }

            this.startAnalysis(formData);
        }

        getFormData() {
            const selectedAgents = Array.from(
                document.querySelectorAll('#agentsContainer input[type="checkbox"]:checked')
            ).map(cb => cb.value);

            const tickersInput = document.getElementById('tickers');
            const initialCashInput = document.getElementById('initialCash');
            const startDateInput = document.getElementById('startDate');
            const endDateInput = document.getElementById('endDate');
            const modelNameInput = document.getElementById('modelName');
            const modelProviderInput = document.getElementById('modelProvider');

            return {
                tickers: tickersInput ? tickersInput.value.split(',').map(t => t.trim()) : [],
                selected_agents: selectedAgents,
                start_date: startDateInput ? startDateInput.value : '',
                end_date: endDateInput ? endDateInput.value : '',
                model_name: modelNameInput ? modelNameInput.value : 'gpt-4',
                model_provider: modelProviderInput ? modelProviderInput.value : 'OPENAI',
                initial_cash: initialCashInput ? parseFloat(initialCashInput.value) : 100000,
                margin_requirement: 0.0
            };
        }

        validateFormData(data) {
            if (data.tickers.length === 0 || data.tickers[0] === '') {
                this.showError('Please enter at least one stock ticker');
                return false;
            }

            if (data.selected_agents.length === 0) {
                this.showError('Please select at least one AI agent');
                return false;
            }

            if (data.initial_cash < 1000) {
                this.showError('Initial cash must be at least $1,000');
                return false;
            }

            return true;
        }

        showError(message) {
            const container = document.getElementById('statusContainer');
            if (!container) return;

            container.innerHTML = `
                <div class="text-center text-danger">
                    <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                    <p>${message}</p>
                </div>
            `;
            console.error('AI Hedge Fund Error:', message);
        }

        updateStatus(message, type = 'info') {
            const container = document.getElementById('statusContainer');
            if (!container) return;

            const iconClass = type === 'success' ? 'fa-check-circle' :
                             type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle';
            const textClass = type === 'success' ? 'text-success' :
                             type === 'error' ? 'text-danger' : 'text-info';

            container.innerHTML = `
                <div class="text-center ${textClass}">
                    <i class="fas ${iconClass} fa-2x mb-2"></i>
                    <p>${message}</p>
                </div>
            `;
        }
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
        // Only initialize if we're on the hedge fund tab
        const hedgeFundTab = document.getElementById('hedge-fund');
        if (hedgeFundTab && hedgeFundTab.classList.contains('active')) {
            new AIHedgeFund();
        }
    });

    // Initialize when hedge fund tab is shown
    const hedgeFundTabButton = document.getElementById('hedge-fund-tab');
    if (hedgeFundTabButton) {
        hedgeFundTabButton.addEventListener('shown.bs.tab', () => {
            new AIHedgeFund();
        });
    }
</script>
