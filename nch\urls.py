"""
URL configuration for nch project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.views.debug import technical_500_response
from django.views.defaults import server_error
import sys

# Set custom admin site headers
admin.site.site_header = "NCH Investment Management"
admin.site.site_title = "NCH Admin"
admin.site.index_title = "Investment Management Administration"

urlpatterns = [
    path('', include('port.urls')),
    path('admin/', admin.site.urls),
    path("accounts/", include("django.contrib.auth.urls")),  # new
    
    path("port/", include("port.urls")),
]

# Allow debugging for superuser only
def handler500(request):
    if request.user.is_superuser:
        return technical_500_response(request, *sys.exc_info())
    else:
        return server_error(request)