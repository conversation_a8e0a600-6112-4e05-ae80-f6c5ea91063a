"""Helper functions for LLM"""

import json
from pydantic import BaseModel
from src.llm.models import get_model, get_model_info
from src.utils.progress import progress
from src.graph.state import AgentState


def call_llm(
    prompt: any,
    pydantic_model: type[BaseModel],
    agent_name: str | None = None,
    state: AgentState | None = None,
    max_retries: int = 3,
    default_factory=None,
) -> BaseModel:
    """
    Makes an LLM call with retry logic, handling both JSON supported and non-JSON supported models.

    Args:
        prompt: The prompt to send to the LLM
        pydantic_model: The Pydantic model class to structure the output
        agent_name: Optional name of the agent for progress updates and model config extraction
        state: Optional state object to extract agent-specific model configuration
        max_retries: Maximum number of retries (default: 3)
        default_factory: Optional factory function to create default response on failure

    Returns:
        An instance of the specified Pydantic model
    """
    
    # Initialize defaults first
    model_name = "gpt-4o"
    model_provider = "OPENAI"

    # Extract model configuration if state is provided and agent_name is available
    if state and agent_name:
        try:
            model_name, model_provider = get_agent_model_config(state, agent_name)
        except Exception as e:
            print(f"Error getting agent model config: {e}")

    # Ensure we have valid values
    if not model_name:
        model_name = "gpt-4o"
    if not model_provider:
        model_provider = "OPENAI"

    print(f"Using model: {model_name} with provider: {model_provider} for agent: {agent_name}")

    try:
        print(f"Initializing model: {model_name} with provider: {model_provider}")
        model_info = get_model_info(model_name, model_provider)
        print(f"Model info: {model_info}")
        llm = get_model(model_name, model_provider)
        print(f"Successfully initialized model: {model_name} ({model_provider})")
        print(f"LLM object: {type(llm)}")
    except Exception as e:
        print(f"Error initializing model {model_name} ({model_provider}): {e}")
        print(f"This is likely an API key issue. Check your environment variables.")
        if default_factory:
            print("Using default factory due to model initialization error")
            return default_factory()
        print("Using create_default_response due to model initialization error")
        return create_default_response(pydantic_model)

    # For non-JSON support models, we can use structured output
    if not (model_info and not model_info.has_json_mode()):
        llm = llm.with_structured_output(
            pydantic_model,
            method="json_mode",
        )

    # Call the LLM with retries
    for attempt in range(max_retries):
        try:
            print(f"Attempt {attempt + 1}/{max_retries}: Calling LLM for agent {agent_name}")
            # Call the LLM
            result = llm.invoke(prompt)
            print(f"LLM response type: {type(result)}")
            print(f"LLM response content: {result}")

            # For non-JSON support models, we need to extract and parse the JSON manually
            if model_info and not model_info.has_json_mode():
                print("Model doesn't support JSON mode, extracting manually")
                parsed_result = extract_json_from_response(result.content)
                if parsed_result:
                    print(f"Successfully parsed JSON: {parsed_result}")
                    return pydantic_model(**parsed_result)
                else:
                    print("Failed to extract JSON from response")
                    raise ValueError("Could not extract valid JSON from response")
            else:
                print("Model supports JSON mode, returning structured result")
                return result

        except Exception as e:
            print(f"Error in LLM call attempt {attempt + 1}: {e}")
            if agent_name:
                progress.update_status(agent_name, None, f"Error - retry {attempt + 1}/{max_retries}")

            if attempt == max_retries - 1:
                print(f"Error in LLM call after {max_retries} attempts: {e}")
                # Use default_factory if provided, otherwise create a basic default
                if default_factory:
                    print("Using default factory")
                    return default_factory()
                print("Using create_default_response")
                return create_default_response(pydantic_model)

    # This should never be reached due to the retry logic above
    return create_default_response(pydantic_model)


def create_default_response(model_class: type[BaseModel]) -> BaseModel:
    """Creates a safe default response based on the model's fields."""
    default_values = {}
    for field_name, field in model_class.model_fields.items():
        if field.annotation == str:
            default_values[field_name] = "Error in analysis, using default"
        elif field.annotation == float:
            default_values[field_name] = 0.0
        elif field.annotation == int:
            default_values[field_name] = 0
        elif hasattr(field.annotation, "__origin__") and field.annotation.__origin__ == dict:
            default_values[field_name] = {}
        else:
            # For other types (like Literal), try to use the first allowed value
            if hasattr(field.annotation, "__args__"):
                default_values[field_name] = field.annotation.__args__[0]
            else:
                default_values[field_name] = None

    return model_class(**default_values)


def extract_json_from_response(content: str) -> dict | None:
    """Extracts JSON from markdown-formatted response."""
    try:
        json_start = content.find("```json")
        if json_start != -1:
            json_text = content[json_start + 7 :]  # Skip past ```json
            json_end = json_text.find("```")
            if json_end != -1:
                json_text = json_text[:json_end].strip()
                return json.loads(json_text)
    except Exception as e:
        print(f"Error extracting JSON from response: {e}")
    return None


def get_agent_model_config(state, agent_name):
    """
    Get model configuration for a specific agent from the state.
    Falls back to global model configuration if agent-specific config is not available.
    """
    request = state.get("metadata", {}).get("request")

    if agent_name == 'portfolio_manager':
        # Get the model and provider from state metadata
        model_name = state.get("metadata", {}).get("model_name", "gpt-4.1")
        model_provider = state.get("metadata", {}).get("model_provider", "OPENAI")
        return model_name, model_provider
    
    if request and hasattr(request, 'get_agent_model_config'):
        # Get agent-specific model configuration
        model_name, model_provider = request.get_agent_model_config(agent_name)
        return model_name, model_provider.value if hasattr(model_provider, 'value') else str(model_provider)
    
    # Fall back to global configuration
    model_name = state.get("metadata", {}).get("model_name", "gpt-4.1")
    model_provider = state.get("metadata", {}).get("model_provider", "OPENAI")
    
    # Convert enum to string if necessary
    if hasattr(model_provider, 'value'):
        model_provider = model_provider.value
    
    return model_name, model_provider
