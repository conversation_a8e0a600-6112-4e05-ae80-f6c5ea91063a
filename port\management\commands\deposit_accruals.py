from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.db.models import Max
from port.models import Bnr, Currency, Deposits, Journal, Operation, Instrument
import pandas as pd
from datetime import datetime, timedelta
import os

class Command(BaseCommand):
    help = 'Calculate accruals for deposits'

    def handle(self, *args, **options):
        self.stdout.write("Starting deposit accruals calculation...")
        
        # 1. Get BNR rates
        df_bnr = pd.DataFrame(
            Bnr.objects.select_related(
                'currency_code'
                ).filter(value_exact__isnull=False
                ).values(
                    'currency_code__currency_code',
                    'date',
                    'value_exact'
                ).order_by('date')
        ).rename(columns={
            'currency_code__currency_code': 'currency',
            'value_exact': 'rate',
        })

        # 2. Get Deposits
        deposits = Deposits.objects.select_related(
            'deposit__currency',
            'deposit__custodian'
        ).values(
            'principal', 'interest_rate', 'convention', 
            'start', 'maturity', 'deposit__currency__currency_code', 
            'deposit__custodian__custodian_code', 'deposit__symbol', 'interest_amount', 
            'new_deposit', 'liquidated'
        ).filter(
            ################# HARD CODED
            maturity__gt='2024-01-31',
            # deposit__symbol='MM2420700338'
            )

        df_results = pd.DataFrame()
        for deposit in deposits: #[list(deposits)[-1]]:
            deposit_info = {
                'principal': float(deposit['principal']),
                'interest_rate': float(deposit['interest_rate']),
                'convention': int(deposit['convention']) if deposit['convention'] in ['360', '365'] else 365,
                'start_date': deposit['start'],
                'end_date': deposit['maturity'],
                'currency': deposit['deposit__currency__currency_code'],
                'custodian': deposit['deposit__custodian__custodian_code'],
                'deposit_id': str(deposit['deposit__symbol']),
                'incasat': float(deposit['interest_amount'] or 0),
                'prima_constituire': deposit['new_deposit'],
                'ultima_lichidare': deposit['liquidated'],
            }
            
            # Calculate accruals for this deposit
            result = self.calculate_accruals_fx(deposit_info, df_bnr)
            df_results = pd.concat([df_results, result])
        
        # Remove lines vot value=0 and value_ron=0
        df_results = df_results[(df_results['value']!=0) | (df_results['value_ron']!=0)]

        # Print 
        print(df_results)

        # Save results
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M")
        output_path = os.path.join(settings.FILE_ROOT, f'accruals/accruals_{timestamp}.xlsx')
        df_results.to_excel(output_path, index=False)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully calculated accruals for {len(deposits)} deposits. Results saved to {output_path}'
            )
        )



    def calculate_accruals_fx(self, inputs, df_bnr):
        """Calculate accruals and FX differences for a deposit"""
        principal = inputs['principal']
        interest_rate = inputs['interest_rate'] / 100
        convention = inputs['convention']
        start_date = pd.Timestamp(inputs['start_date'])
        end_date = pd.Timestamp(inputs['end_date'])
        currency = inputs['currency']
        deposit_id = inputs['deposit_id']
        custodian = inputs['custodian']
        incasat = inputs['incasat']
        prima_constituire = inputs['prima_constituire']
        ultima_lichidare = inputs['ultima_lichidare']

        # Calculate end of months between start_date and minimum of today and end_date
        max_date = pd.Timestamp.today().replace(day=1)
        end_of_months = pd.date_range(
            start=start_date,
            end=min(max_date, end_date),
            freq='ME'
        )
        end_of_months = [x for x in end_of_months if ((x>=start_date) and (x<=end_date))]
        print(custodian, deposit_id, 'end_of_months:', end_of_months)

        # BNR Rates
        bnr = df_bnr[df_bnr['currency']==currency].drop(columns='currency')
        bnr = bnr.sort_values(by='date', ascending=False) # Very important, do not re-sort
        bnr['date'] = pd.to_datetime(bnr['date'])

        # Initialize result list
        results = []

        # Step 1: CONSTITUIRE_DEP_VALUTA 
        initial_bnr_rate = bnr[bnr['date']<start_date]['rate'].iloc[0]
        operation_code = 'CONSTITUIRE_DEP_VALUTA' if currency != 'RON' else 'CONSTITUIRE_DEP_LEI'
        date_str = str(start_date)[:10]
        initial_value_ron = principal*initial_bnr_rate
        results.append({
            'date': date_str,
            'operation': operation_code,
            'value': -principal,
            'currency': currency,
            'bnr': initial_bnr_rate,
            'value_ron': -initial_value_ron,
            'quantity': principal,
            'transactionid': f"{deposit_id} CONSTITUIRE {date_str}",
            'idx': 'ADAUGA' if prima_constituire else None,
            'storno': False
        })

        # Step 2: Calculate monthly accruals
        last_accrual_date = start_date
        last_bnr_rate = initial_bnr_rate
        last_bnr_rate_end = initial_bnr_rate
        accrued_interest_cumulative = 0
        accrued_interest_cumulative_ron = 0
        accrued_fx_cumulative_ron = 0

        for date in end_of_months:
            date_str = str(date)[:10]
            # Calculate accrual for the period
            accrual_days = (date - last_accrual_date).days
            last_accrual_date = date

            # print('accrual_days:', accrual_days)

            # Get BNR rate for the current date and rate for the end of month
            bnr_rate = bnr[bnr['date']<date]['rate'].iloc[0]
            bnr_rate_end = bnr[bnr['date']<=date]['rate'].iloc[0]

            # Calculate accrued interest
            accrued_interest = (principal * interest_rate * accrual_days) / convention
            accrued_interest_ron = accrued_interest * bnr_rate
            accrued_interest_cumulative += accrued_interest
            accrued_interest_cumulative_ron += accrued_interest_ron

            # Add accrual entry
            operation_code = 'ACCRUAL_INTEREST_VALUTA' if currency != 'RON' else 'ACCRUAL_INTEREST_LEI'
            results.append({
                'date': date_str,
                'operation': operation_code,
                'value': accrued_interest,
                'cumulative_interest': accrued_interest_cumulative,
                'currency': currency,
                'bnr': bnr_rate,
                'value_ron': accrued_interest_ron,
                'transactionid': f"{deposit_id} ACC {date_str}",
                'storno': False
            })

            # Calculate FX differences
            if currency != 'RON':

                # For deposit principal
                fx_diff_principal = principal * (bnr_rate_end - last_bnr_rate_end)
                operation = 'FX_DIF_DEP_PLUS' if fx_diff_principal >= 0 else 'FX_DIF_DEP_MINUS'
                if abs(fx_diff_principal) >= 0.01:
                    results.append({
                        'date': date_str,
                        'operation': operation,
                        'value': fx_diff_principal,
                        'currency': 'RON',
                        'bnr': 1,
                        'value_ron': fx_diff_principal,
                        'transactionid': f"{deposit_id} FXDEP {date_str}",
                        'storno': False
                    })

                # For accrued interest
                fx_diff_interest = accrued_interest_cumulative * bnr_rate_end - \
                                  accrued_interest_cumulative_ron - accrued_fx_cumulative_ron
                accrued_fx_cumulative_ron += fx_diff_interest
                operation = 'FX_DIF_ACCRUAL_PLUS' if fx_diff_interest >= 0 else 'FX_DIF_ACCRUAL_MINUS'
                if abs(fx_diff_interest) >= 0.01:
                    results.append({
                        'date': date_str,
                        'operation': operation,
                        'value': fx_diff_interest,
                        'currency': 'RON',
                        'bnr': 1,
                        'value_ron': fx_diff_interest,
                        'transactionid': f"{deposit_id} FXINT {date_str}",
                        'storno': False
                    })

            # Reset reference exchange rates
            last_bnr_rate_end = bnr_rate_end
            last_bnr_rate = bnr_rate

        

        # Step 3: Handle maturity if deposit has ended
        if end_date <= max_date:
            end_date_str = str(end_date)[:10]
            bnr_rate = bnr[bnr['date']<end_date]['rate'].iloc[0]
            accrual_days = (end_date - last_accrual_date).days

            # print('accrual_days:', accrual_days)

            # if abs(accrued_interest_cumulative - incasat) > 0.01:
            #     print('Diferente dobanzi: accrued_interest_cumulative', accrued_interest_cumulative, 'incasat', incasat, 'deposit_id', deposit_id)

            # Add interest received entry if applicable
            if incasat > 0:
                operation = 'INC_DOBANDA_LEI' if currency == 'RON' else 'INC_DOBANDA_VALUTA'
                results.append({
                    'date': end_date_str,
                    'operation': operation,
                    'value': incasat,
                    'cumulative_interest': accrued_interest_cumulative,
                    'currency': currency,
                    'bnr': bnr_rate,
                    'value_ron': incasat * bnr_rate, 
                    'incasat': incasat,
                    # 'calculat_vs_incasat': accrued_interest_cumulative - incasat, 
                    'transactionid': f"{deposit_id} DOBANDA {end_date_str}",
                    'idx': 'ADAUGA' if ultima_lichidare else None,
                    'storno': False
                })

            # Add accrual reversal
            operation = 'ACCRUAL_INTEREST_LEI' if currency == 'RON' else 'ACCRUAL_INTEREST_VALUTA'
            if accrued_interest_cumulative > 0:
                results.append({
                    'date': end_date_str,
                    'operation': operation,
                    'value': -accrued_interest_cumulative,
                    'currency': currency,
                    'bnr': bnr_rate,
                    'storno': True,
                    'value_ron': -accrued_interest_cumulative * bnr_rate, 
                    'transactionid': f"{deposit_id} REVERSAL {end_date_str}",
                    'idx': 'ADAUGA' if ultima_lichidare else None
                })

            # FX diff deposit
            if currency != 'RON':
                fx_diff_principal = principal * (bnr_rate - last_bnr_rate_end)
                operation= 'FX_DIF_DEP_PLUS' if (fx_diff_principal>=0.0) else 'FX_DIF_DEP_MINUS'
                results.append({
                    'date': end_date_str,
                    'operation': operation,
                    'value': fx_diff_principal,
                    'currency': 'RON',
                    'bnr': 1,
                    'value_ron': fx_diff_principal,
                    'transactionid': f"{deposit_id} FXDEP {end_date_str}",
                    'storno': False
                })

                if len(end_of_months) > 0:
                    accrued_fx_interest_ron = accrued_interest_cumulative * bnr_rate - accrued_interest_cumulative_ron - accrued_fx_cumulative_ron
                    operation = 'FX_DIF_ACCRUAL_PLUS' if (accrued_fx_interest_ron>=0.0) else 'FX_DIF_ACCRUAL_MINUS'
                    # print('accrued_interest_cumulative', accrued_interest_cumulative)
                    # print('bnr_rate', bnr_rate) 
                    # print('accrued_interest_cumulative_ron', accrued_interest_cumulative_ron)
                    # print('accrued_fx_cumulative_ron', accrued_fx_cumulative_ron)
                    results.append({
                        'date': end_date_str,
                        'operation': operation,
                        'value': accrued_fx_interest_ron,
                        'currency': 'RON',
                        'bnr': 1,
                        'value_ron': accrued_fx_interest_ron,
                        'transactionid': f"{deposit_id} FXINT {end_date_str}",
                        'storno': False
                    })  


            ########

            # Add maturity entry
            operation = 'MATURITATE_DEP_VALUTA' if currency != 'RON' else 'MATURITATE_DEP_LEI'
            final_value_ron = principal * bnr_rate
            results.append({
                'date': end_date_str,
                'operation': operation,
                'value': principal,
                'currency': currency,
                'bnr': bnr_rate,
                'value_ron': final_value_ron,
                'quantity': -principal,
                'transactionid': f"{deposit_id} MAT {end_date_str}",
                'idx': 'ADAUGA' if ultima_lichidare else None,
                'storno': False
            })

        
        # Convert results into a DataFrame
        output_df = pd.DataFrame(results)
        output_df['custodian'] = custodian        
        output_df['ubo'] = "DD"
        output_df['partner'] = output_df['custodian']
        output_df['account'] = output_df['custodian'] + '_' + output_df['currency']
        output_df['instrument'] = output_df['custodian'] + '_' + str(deposit_id) 
        # output_df['instrument__type'] = 'DEPOZIT'
        output_df['details'] = str(deposit_id) + ' ' + output_df['operation']
        output_df['quantity'] = output_df['quantity'].fillna(0)

        if 'storno' not in output_df.columns:
            output_df['storno'] = False
        else:
            # print(output_df[output_df['storno'].isna()])
            output_df.loc[output_df['storno'], 'details'] = 'STORNO ' + output_df['details']

        # Rounding
        roundx = 2
        output_df['value'] = output_df['value'].round(roundx)
        output_df['value_ron'] = output_df['value_ron'].round(roundx)

        # Query existing journal IDs
        journal_map = pd.DataFrame(
            Journal.objects.filter(
                transactionid__in=output_df['transactionid'].tolist()
            ).values('id', 'transactionid')
            )

        if len(journal_map) > 0:
            # Merge journal IDs into output_df
            output_df = output_df.merge(
                journal_map,
                on='transactionid',
                how='left'
            )
        else:
            # If no matching journal records found, add empty id column
            output_df['id'] = None

        

        # print(output_df)

        return output_df
