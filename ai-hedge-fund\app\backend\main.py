from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from app.backend.routes import api_router
from app.backend.database.connection import engine
from app.backend.database.models import Base

app = FastAPI(title="AI Hedge Fund API", description="Backend API for AI Hedge Fund", version="0.1.0")

# Initialize database tables (this is safe to run multiple times)
Base.metadata.create_all(bind=engine)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://localhost:8082",  # Docker frontend service
        "http://frontend:8080",   # Docker internal network
        "*"  # Allow all origins for development
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include all routes
app.include_router(api_router)
