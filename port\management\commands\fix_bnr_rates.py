from django.core.management.base import BaseCommand
from django.conf import settings
from port.models import Journal, Bnr, Instrument
import pandas as pd
from datetime import datetime, date
from dateutil.relativedelta import relativedelta

class Command(BaseCommand):
    help = 'Calculate bond accruals and related operations'

    def __init__(self):
        super().__init__()
        
    def handle(self, *args, **options):

        # Get related data
        journal_qs = Journal.objects.select_related(
                'operation',
                'instrument',
                'instrument__currency',
            ).values(
                'id',
                'transactionid',
                'ubo__ubo_code',
                'custodian__custodian_code',
                'instrument__symbol',
                'instrument__currency__currency_code',
                'operation__operation_code',
                'date',
                'value',
                'bnr',
                'value_ron',
            )

        df = pd.DataFrame(list(journal_qs)).rename(columns={
                'instrument__currency__currency_code': 'currency',
                'instrument__symbol': 'symbol',
                'custodian__custodian_code': 'custodian',
                'operation__operation_code': 'operation',
                'ubo__ubo_code': 'ubo',
            }).sort_values(['date', 'currency'])
        df["date"] = pd.to_datetime(df["date"])

        print(len(df), 'records in journal')

        error_ron = df[
            (df['currency'] == 'RON')
            & (df['bnr']!=1)
            ].copy()

        if len(error_ron)>0:
            print('error_ron:')
            print(error_ron)
        else:
            print('RON: No errors')

        bnr_qs = Bnr.objects.filter(
            currency_code__currency_code__in=['EUR', 'USD', 'MXN']
            ).select_related(
                'currency_code'
            ).values(
                'currency_code__currency_code',
                'date',
                # 'value',
                'value_exact'
            )
        
        df_bnr = pd.DataFrame(list(bnr_qs)).rename(columns={
            'currency_code__currency_code': 'currency',
            # 'value': 'bnr_ieri',
            'value_exact': 'bnrx',
            })

        # Remove holidays and set date format
        df_bnr = df_bnr[~df_bnr['bnrx'].isna()]
        df_bnr["date"] = pd.to_datetime(df_bnr["date"])
        df_bnr.sort_values(['date', 'currency'], inplace=True)

        # Attach BNR rates
        df = pd.merge_asof(
                    left=df, right=df_bnr, on='date', by=['currency'],
                    allow_exact_matches=False, direction='backward',
                    )
        df.loc[df['currency']=='RON', 'bnrx'] = 1

        # Attach BNR End of month
        df_bnr.rename(columns={'bnrx': 'bnr_eom'}, inplace=True)
        df = pd.merge_asof(
                    left=df, right=df_bnr, on='date', by=['currency'],
                    allow_exact_matches=True, direction='backward',
                    )

        # DEBUG
        print(df[df['transactionid']=='3496921725'])

        errors = df[
            (
                ((df['bnr']- df['bnrx']).abs()>0.0001) 
                # | ((df['bnr']== df['bnr_eom']).abs()>0.01)
            )
            ].copy()

        # operations_ok = [
        #     'FX_DIF_ACCRUAL_MINUS', 'FX_DIF_ACCRUAL_PLUS',
        #     'FX_DIF_DEP_MINUS', 'FX_DIF_DEP_PLUS',
        #     'FX_DIF_BOND_MINUS', 'FX_DIF_BOND_PLUS'
        # ]

        # errors = errors[~(errors['operation'].isin(operations_ok))]

        print(len(errors), 'errors')
        print(errors)

        # Convert date to date type
        errors['date'] = pd.to_datetime(errors['date'])
        errors['bnr_incorrect'] = errors['bnr'] 
        errors['bnr'] = errors['bnrx']
        errors['value_ron'] = (errors['bnr'] * errors['value']).round(2)



        # Save errors as excel file
        save_path = settings.FILE_ROOT + f"reports/bnr_errors.xlsx"
        with pd.ExcelWriter(save_path) as writer:
            # df.to_excel(writer, sheet_name='Existing', index=False)
            errors.to_excel(writer, sheet_name='BNR Errors', index=False)




        # dx['instrument'] = dx['custodian'] + '_RON'
        # dx['account'] = dx['instrument'] 
        # dx['partner'] = dx['custodian'] 
        # dx['value_ron'] = dx['value_ron'].round(2)
        # dx['value'] = dx['value_ron'] 

        # save_path = settings.FILE_ROOT + f"reports/dif_curs_schimb.xlsx"
        # with pd.ExcelWriter(save_path) as writer:
        #     df.to_excel(writer, sheet_name='Existing', index=False)
        #     dx.to_excel(writer, sheet_name='DifCursSchimb', index=False)

        if len(errors)>0:
            print(errors)
            print('Errors saved to', save_path)
        else:
            print('No errors')


