""" Export operations from """
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings 
from port.models import Bnr, Currency
import datetime
from pandas.tseries.offsets import BusinessDay
import numpy as np

import requests
import xmltodict

class Command(BaseCommand):

    def handle(self, *args, **options):
        import pandas as pd

        def get_bnr(url):
            response = requests.get(url)

            my_dict = xmltodict.parse(response.content)['DataSet']['Body']['Cube']  
            df = pd.DataFrame(my_dict).explode('Rate', ignore_index=True)
            df = df.join(pd.json_normalize(df['Rate'])).drop(columns='Rate')

            df.rename(columns={ 
                '@date': 'date',
                '@currency': 'currency',
                '#text': 'value',
                '@multiplier': 'multiplier',
                }, inplace=True)
            
            df['value'] = df['value'].astype(float)
            df.loc[~df['multiplier'].isna(),'value'] = df['value'] / df['multiplier'].astype(float)

            df['date']= pd.to_datetime(df['date'])
            
            # Preserve map date:value
            dx = df[['date', 'currency', 'value']].copy()
            dx.rename(columns={'value': 'value_exact'}, inplace=True)

            # Assign date to previous / next day
            bdays = sorted(df['date'].unique())
            bdays += [bdays[-1] + BusinessDay()]
            nextbday = {}
            for i in range(len(bdays)-1):
                nextbday[bdays[i]] = bdays[i+1]

            df['date'] = df['date'].map(nextbday)
            df.drop(columns=['multiplier'], inplace=True)
            df = df[~df['date'].isna()]

            df = df.merge(dx, how='left', on=['date', 'currency'], validate='many_to_one')

            return df
        
        FULL = False

        if FULL:
            df = pd.concat([
                get_bnr('https://www.bnr.ro/files/xml/years/nbrfxrates2023.xml'),
                get_bnr('https://www.bnr.ro/files/xml/years/nbrfxrates2024.xml'),
            ])
        else:
            df = get_bnr( 'https://bnr.ro/nbrfxrates10days.xml')

        bnr = pd.DataFrame()
        for c in df['currency'].unique():
            dx = df[df['currency']==c].sort_values(by='date').copy()
            
            # Backfill dates
            d1 = min(dx['date'])
            d2 = max(dx['date'])
            dates = [d1 + datetime.timedelta(days=x) 
                    for x in range((d2-d1).days )]
            dates_missing = list(set(dates)-set(dx['date'].to_list()))
            dates_missing = [str(x)[:10] for x in dates_missing]

            dx['date'] = dx['date'].astype('str').str[:10]
            for d in dates_missing:
                blank = pd.DataFrame(columns=['date', 'currency'], data=[[d,c]])
                dx = pd.concat([dx, blank]) 

            dx.sort_values(by=['date'], inplace=True, ignore_index=True)
            dx['value'] = dx['value'].bfill()
            bnr = pd.concat([bnr, dx])

        # add RON
        for d in bnr['date'].unique():
            ron = pd.DataFrame(columns=['date' , 'currency', 'value', 'value_exact'], data=[[d, 'RON', 1.0, 1.0]])
            bnr = pd.concat([bnr, ron]) 

        # Add new currencies in Currency table
        currencies = df['currency'].unique()
        for c in currencies:
            existing = Currency.objects.filter(currency_code=c).all().values()
            if len(existing)==0:
                obj = Currency(
                    currency_code = c,
                    currency_name = c, 
                )
                obj.save()

        # Prepare update
        bnr = bnr.replace(np.nan, None)
        model_instances = [
            Bnr(
                currency_code = Currency.objects.get(currency_code = row['currency']),
                date = row['date'], 
                value = row['value'], 
                value_exact = row['value_exact'], 
            )
            for i, row in bnr.iterrows()]
        
        # Upload model_instances to database
        unique = ['currency_code', 'date']
        update_fields = unique + ['value', 'value_exact']
        Bnr.objects.bulk_create(
            model_instances, 
            update_conflicts=True,
            unique_fields=unique,
            update_fields=update_fields,
        )

        print(len(bnr), 'records')
        print('All done')


