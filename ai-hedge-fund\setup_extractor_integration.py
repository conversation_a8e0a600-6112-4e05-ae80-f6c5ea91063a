#!/usr/bin/env python
"""
Setup script for integrating AI Hedge Fund with the Extractor App
"""
import os
import sys
import requests
from pathlib import Path


def test_extractor_connection(url: str, username: str, password: str) -> bool:
    """Test connection to the extractor app"""
    try:
        print(f"Testing connection to {url}...")
        
        # Test authentication
        auth_url = f"{url}/auth/login/"
        response = requests.post(auth_url, json={
            'username': username,
            'password': password
        }, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            access_token = data.get('access')
            
            if access_token:
                print("✅ Authentication successful")
                
                # Test credentials endpoint
                headers = {'Authorization': f'Bearer {access_token}'}
                creds_url = f"{url}/credentials/"
                creds_response = requests.get(creds_url, headers=headers, timeout=10)
                
                if creds_response.status_code == 200:
                    creds_data = creds_response.json()
                    ai_creds = creds_data.get('ai_hedge_fund_credentials', [])
                    print(f"✅ Found {len(ai_creds)} AI hedge fund credential set(s)")
                    return True
                else:
                    print(f"❌ Failed to fetch credentials: {creds_response.status_code}")
                    return False
            else:
                print("❌ No access token received")
                return False
        else:
            print(f"❌ Authentication failed: {response.status_code} - {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def create_env_file(config: dict):
    """Create .env file with extractor configuration"""
    env_path = Path('.env')
    
    # Read existing .env if it exists
    existing_vars = {}
    if env_path.exists():
        with open(env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    existing_vars[key.strip()] = value.strip()
    
    # Update with new configuration
    existing_vars.update(config)
    
    # Write back to .env
    with open(env_path, 'w') as f:
        f.write("# AI Hedge Fund Configuration\n")
        f.write("# Generated by setup_extractor_integration.py\n\n")
        
        f.write("# Extractor App Integration\n")
        for key in ['EXTRACTOR_URL', 'EXTRACTOR_USERNAME', 'EXTRACTOR_PASSWORD', 
                   'EXTRACTOR_CREDENTIAL_ID', 'EXTRACTOR_CREDENTIAL_NAME', 
                   'USE_LOCAL_FALLBACK', 'CREDENTIALS_CACHE_TIMEOUT']:
            if key in existing_vars:
                f.write(f"{key}={existing_vars[key]}\n")
        
        f.write("\n# Local Fallback Credentials\n")
        for key in ['ANTHROPIC_API_KEY', 'DEEPSEEK_API_KEY', 'GROQ_API_KEY',
                   'GOOGLE_API_KEY', 'OPENAI_API_KEY', 'FINANCIAL_DATASETS_API_KEY',
                   'OPENAI_API_BASE']:
            if key in existing_vars:
                f.write(f"{key}={existing_vars[key]}\n")
        
        # Add any other existing variables
        other_vars = {k: v for k, v in existing_vars.items() 
                     if not k.startswith(('EXTRACTOR_', 'ANTHROPIC_', 'DEEPSEEK_', 
                                         'GROQ_', 'GOOGLE_', 'OPENAI_', 'FINANCIAL_',
                                         'USE_LOCAL_', 'CREDENTIALS_'))}
        
        if other_vars:
            f.write("\n# Other Configuration\n")
            for key, value in other_vars.items():
                f.write(f"{key}={value}\n")
    
    print(f"✅ Configuration saved to {env_path}")


def main():
    print("🚀 AI Hedge Fund - Extractor App Integration Setup")
    print("=" * 60)
    
    # Get extractor app configuration
    print("\n📡 Extractor App Configuration")
    extractor_url = input("Extractor app URL (default: http://localhost:8000): ").strip()
    if not extractor_url:
        extractor_url = "http://localhost:8000"
    
    username = input("Extractor app username: ").strip()
    if not username:
        print("❌ Username is required")
        return
    
    password = input("Extractor app password: ").strip()
    if not password:
        print("❌ Password is required")
        return
    
    # Test connection
    print(f"\n🔍 Testing connection...")
    if not test_extractor_connection(extractor_url, username, password):
        print("❌ Connection test failed. Please check your configuration.")
        retry = input("Do you want to continue anyway? (y/n): ")
        if retry.lower() != 'y':
            return
    
    # Get credential selection method
    print(f"\n🔑 Credential Selection")
    print("Choose how to select AI hedge fund credentials:")
    print("1. Use specific credential ID")
    print("2. Use credential name")
    print("3. Use first active credential set")
    
    choice = input("Enter choice (1-3, default: 3): ").strip()
    
    credential_id = ""
    credential_name = ""
    
    if choice == "1":
        credential_id = input("Enter credential ID: ").strip()
    elif choice == "2":
        credential_name = input("Enter credential name: ").strip()
    # For choice 3 or default, we leave both empty
    
    # Fallback configuration
    print(f"\n🔄 Fallback Configuration")
    use_fallback = input("Use local environment variables as fallback? (y/n, default: y): ").strip()
    use_fallback = use_fallback.lower() != 'n'
    
    cache_timeout = input("Credentials cache timeout in seconds (default: 300): ").strip()
    if not cache_timeout:
        cache_timeout = "300"
    
    # Create configuration
    config = {
        'EXTRACTOR_URL': extractor_url,
        'EXTRACTOR_USERNAME': username,
        'EXTRACTOR_PASSWORD': password,
        'USE_LOCAL_FALLBACK': 'true' if use_fallback else 'false',
        'CREDENTIALS_CACHE_TIMEOUT': cache_timeout
    }
    
    if credential_id:
        config['EXTRACTOR_CREDENTIAL_ID'] = credential_id
    if credential_name:
        config['EXTRACTOR_CREDENTIAL_NAME'] = credential_name
    
    # Preserve existing local credentials if using fallback
    if use_fallback:
        print(f"\n🔐 Local Fallback Credentials")
        print("Current local environment variables will be preserved as fallback.")
        
        # Check for existing credentials
        local_creds = [
            'ANTHROPIC_API_KEY', 'DEEPSEEK_API_KEY', 'GROQ_API_KEY',
            'GOOGLE_API_KEY', 'OPENAI_API_KEY', 'FINANCIAL_DATASETS_API_KEY',
            'OPENAI_API_BASE'
        ]
        
        found_creds = []
        for cred in local_creds:
            if os.getenv(cred):
                found_creds.append(cred)
                config[cred] = os.getenv(cred)
        
        if found_creds:
            print(f"Found existing credentials: {', '.join(found_creds)}")
        else:
            print("No existing local credentials found.")
            add_local = input("Do you want to add local fallback credentials now? (y/n): ")
            if add_local.lower() == 'y':
                for cred in local_creds:
                    value = input(f"{cred} (optional): ").strip()
                    if value:
                        config[cred] = value
    
    # Create .env file
    print(f"\n💾 Saving Configuration")
    create_env_file(config)
    
    # Final instructions
    print(f"\n🎉 Setup Complete!")
    print(f"")
    print(f"📋 Next Steps:")
    print(f"   1. The AI hedge fund app will now fetch credentials from the extractor app")
    print(f"   2. Start your AI hedge fund application normally")
    print(f"   3. Check logs to verify credential fetching is working")
    print(f"")
    print(f"🔧 Configuration:")
    print(f"   • Extractor URL: {extractor_url}")
    print(f"   • Username: {username}")
    if credential_id:
        print(f"   • Credential ID: {credential_id}")
    elif credential_name:
        print(f"   • Credential Name: {credential_name}")
    else:
        print(f"   • Using first active credential set")
    print(f"   • Local fallback: {'Enabled' if use_fallback else 'Disabled'}")
    print(f"   • Cache timeout: {cache_timeout} seconds")
    
    print(f"\n🔄 To reconfigure, run this script again or edit the .env file directly.")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Setup cancelled by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
