import logging
from django.core.management.base import BaseCommand, CommandError
from port.services.provider.deposits_service import DepositsService

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Process deposit accruals and create journal entries using Django ORM'

    def add_arguments(self, parser):
        parser.add_argument(
            '--deposit-symbol',
            type=str,
            help='Process only the specified deposit symbol',
        )
        parser.add_argument(
            '--summary-only',
            action='store_true',
            help='Show summary without processing accruals',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )

    def handle(self, *args, **options):
        """Handle the command execution"""
        if options['verbose']:
            logging.getLogger('port.services.provider.deposits_service').setLevel(logging.DEBUG)
        
        deposit_symbol = options.get('deposit_symbol')
        summary_only = options.get('summary_only', False)
        
        try:
            service = DepositsService()
            
            if summary_only:
                self._show_summary(service, deposit_symbol)
            else:
                self._process_deposits(service, deposit_symbol)
                
        except Exception as e:
            logger.error(f"Command failed: {str(e)}")
            raise CommandError(f"Failed to process deposits: {str(e)}")

    def _show_summary(self, service: DepositsService, deposit_symbol: str = None):
        """Show deposit summary without processing"""
        self.stdout.write(self.style.SUCCESS("=== DEPOSITS SUMMARY ==="))
        
        summary = service.get_deposit_summary(deposit_symbol)
        
        self.stdout.write(f"Total deposits: {summary['total_deposits']}")
        self.stdout.write("")
        
        for deposit in summary['deposits']:
            self.stdout.write(f"Deposit: {deposit['symbol']}")
            self.stdout.write(f"  Custodian: {deposit['custodian']}")
            self.stdout.write(f"  Currency: {deposit['currency']}")
            self.stdout.write(f"  Principal: {deposit['principal']:,.2f}")
            self.stdout.write(f"  Interest Rate: {deposit['interest_rate']:.2f}%")
            self.stdout.write(f"  Period: {deposit['start_date']} to {deposit['end_date']}")
            self.stdout.write(f"  Expected Interest: {deposit['expected_interest']:,.2f}")
            self.stdout.write(f"  Collected Interest: {deposit['collected_interest']:,.2f}")
            self.stdout.write(f"  Existing Journal Entries: {deposit['existing_journal_entries']}")
            
            if deposit['is_new']:
                self.stdout.write(self.style.WARNING("  Status: NEW DEPOSIT"))
            if deposit['is_liquidated']:
                self.stdout.write(self.style.WARNING("  Status: LIQUIDATED"))
            
            self.stdout.write("")

    def _process_deposits(self, service: DepositsService, deposit_symbol: str = None):
        """Process deposits and create journal entries"""
        self.stdout.write(self.style.SUCCESS("=== PROCESSING DEPOSITS ==="))
        
        if deposit_symbol:
            self.stdout.write(f"Processing deposit: {deposit_symbol}")
        else:
            self.stdout.write("Processing all deposits...")
        
        self.stdout.write("")
        
        # Process deposits
        results = service.calculate_and_store_accruals(deposit_symbol)
        
        # Display results
        self.stdout.write(self.style.SUCCESS("=== PROCESSING RESULTS ==="))
        self.stdout.write(f"Processed deposits: {results['processed_deposits']}")
        self.stdout.write(f"Created journal entries: {results['created_journals']}")
        
        if results['errors']:
            self.stdout.write(self.style.ERROR(f"Errors: {len(results['errors'])}"))
            for error in results['errors']:
                self.stdout.write(self.style.ERROR(f"  - {error}"))
        
        self.stdout.write("")
        
        # Display detailed results for each deposit
        for deposit_result in results['deposit_results']:
            self.stdout.write(f"Deposit: {deposit_result['deposit_symbol']}")
            self.stdout.write(f"  Custodian: {deposit_result['custodian']}")
            self.stdout.write(f"  Currency: {deposit_result['currency']}")
            self.stdout.write(f"  Accrual entries calculated: {deposit_result['accrual_entries']}")
            self.stdout.write(f"  Journal entries created: {deposit_result['created_journals']}")
            
            # Show sample entries if verbose
            if len(deposit_result['entries']) > 0:
                self.stdout.write("  Sample entries:")
                for i, entry in enumerate(deposit_result['entries'][:3]):  # Show first 3 entries
                    self.stdout.write(f"    {i+1}. {entry['date']} - {entry['operation']} - "
                                    f"{entry['value']:,.2f} {entry['currency']} "
                                    f"({entry['value_ron']:,.2f} RON)")
                
                if len(deposit_result['entries']) > 3:
                    self.stdout.write(f"    ... and {len(deposit_result['entries']) - 3} more entries")
            
            self.stdout.write("")
        
        if results['processed_deposits'] > 0:
            self.stdout.write(self.style.SUCCESS("✓ Processing completed successfully"))
        else:
            self.stdout.write(self.style.WARNING("⚠ No deposits were processed"))
