import logging
from decimal import Decimal
from typing import List, Dict
from django.db.models import QuerySet
from django.core.exceptions import ObjectDoesNotExist, MultipleObjectsReturned
from django.utils import timezone

from port.models import (
    Bnr, Currency, Deposits, Journal, Operation, Instrument,
    Ubo, <PERSON>ust<PERSON>ian, Account, Partner
)

logger = logging.getLogger(__name__)


class DepositsService:
    """Service for calculating deposit accruals and creating journal entries using Django ORM"""

    def __init__(self):
        """Initialize the service with required base objects"""
        try:
            self.ubo = Ubo.objects.get(ubo_code='DD')
        except ObjectDoesNotExist:
            logger.error("UBO 'DD' not found. Please create it first.")
            self.ubo = None
        except MultipleObjectsReturned:
            logger.error("Multiple UBOs with code 'DD' found. Please ensure uniqueness.")
            self.ubo = None

    def calculate_and_store_accruals(self) -> Dict:
        """
        Calculate accruals for deposits and automatically create journal entries

        Args:
            deposit_symbol: Optional specific deposit symbol to process

        Returns:
            Dict with processing results
        """
        if not self.ubo:
            raise ValueError("Cannot process deposits without valid UBO")

        logger.info("Starting deposit accruals calculation...")

        # Get BNR rates using Django ORM
        bnr_rates = self._get_bnr_rates()

        # Get deposits to process
        deposits_queryset = self._get_deposits_queryset()

        results = {
            'processed_deposits': 0,
            'created_journals': 0,
            'errors': [],
            'deposit_results': []
        }

        for deposit in deposits_queryset:
            try:
                deposit_result = self._process_single_deposit(deposit, bnr_rates)
                results['deposit_results'].append(deposit_result)
                results['processed_deposits'] += 1
                results['created_journals'] += deposit_result['created_journals']

                logger.info(f"Processed deposit {deposit.deposit.symbol} - "
                          f"Created {deposit_result['created_journals']} journal entries")

            except Exception as e:
                error_msg = f"Error processing deposit {deposit.deposit.symbol}: {str(e)}"
                logger.error(error_msg)
                results['errors'].append(error_msg)

        logger.info(f"Completed processing {results['processed_deposits']} deposits, "
                   f"created {results['created_journals']} journal entries")

        return results

    def _get_bnr_rates(self) -> QuerySet:
        """Get BNR exchange rates using Django ORM"""
        return Bnr.objects.select_related('currency_code').filter(
            value_exact__isnull=False
        ).order_by('date')

    def _get_deposits_queryset(self, deposit_symbol: str = None) -> QuerySet:
        """Get deposits queryset with related objects"""
        queryset = Deposits.objects.select_related(
            'deposit__currency',
            'deposit__custodian'
        )

        if deposit_symbol:
            queryset = queryset.filter(deposit__symbol=deposit_symbol)

        return queryset

    def _process_single_deposit(self, deposit: Deposits, bnr_rates: QuerySet) -> Dict:
        """Process a single deposit and create journal entries"""
        deposit_info = self._extract_deposit_info(deposit)

        # Calculate accruals
        accrual_entries = self._calculate_accruals_for_deposit(deposit_info, bnr_rates)

        # Create journal entries
        processed_journals = self._create_journal_entries(accrual_entries, deposit)

        return {
            'deposit_symbol': deposit.deposit.symbol,
            'custodian': deposit.deposit.custodian.custodian_code,
            'currency': deposit.deposit.currency.currency_code,
            'accrual_entries': len(accrual_entries),
            'created_journals': processed_journals,
            'entries': accrual_entries
        }

    def _extract_deposit_info(self, deposit: Deposits) -> Dict:
        """Extract deposit information into a dictionary"""
        return {
            'principal': float(deposit.principal),
            'interest_rate': float(deposit.interest_rate),
            'convention': int(deposit.convention) if deposit.convention in ['360', '365'] else 365,
            'start_date': deposit.start,
            'end_date': deposit.maturity,
            'currency': deposit.deposit.currency.currency_code,
            'custodian': deposit.deposit.custodian.custodian_code,
            'deposit_id': deposit.deposit.symbol,
            'incasat': float(deposit.interest_amount or 0),
            'prima_constituire': deposit.new_deposit,
            'ultima_lichidare': deposit.liquidated,
        }

    def _calculate_accruals_for_deposit(self, deposit_info: Dict, bnr_rates: QuerySet) -> List[Dict]:
        """Calculate accruals and FX differences for a deposit using Django ORM"""
        principal = deposit_info['principal']
        interest_rate = deposit_info['interest_rate'] / 100
        convention = deposit_info['convention']
        start_date = deposit_info['start_date']
        end_date = deposit_info['end_date']
        currency = deposit_info['currency']
        deposit_id = deposit_info['deposit_id']
        custodian = deposit_info['custodian']
        incasat = deposit_info['incasat']
        prima_constituire = deposit_info['prima_constituire']
        ultima_lichidare = deposit_info['ultima_lichidare']

        # Calculate end of months between start_date and minimum of today and end_date
        today = timezone.now().date()
        max_date = today.replace(day=1)  # First day of current month

        # Generate end of month dates
        end_of_months = self._generate_end_of_months(start_date, min(max_date, end_date))
        end_of_months = [x for x in end_of_months if start_date <= x <= end_date]

        logger.debug(f"{custodian} {deposit_id} end_of_months: {end_of_months}")

        # Get BNR rates for this currency
        currency_rates = self._get_currency_rates(bnr_rates, currency)

        # Initialize result list
        results = []

        # Step 1: CONSTITUIRE_DEP_VALUTA/LEI
        initial_bnr_rate = self._get_bnr_rate_before_date(currency_rates, start_date)
        operation_code = 'CONSTITUIRE_DEP_VALUTA' if currency != 'RON' else 'CONSTITUIRE_DEP_LEI'
        initial_value_ron = principal * initial_bnr_rate

        results.append({
            'date': start_date,
            'operation': operation_code,
            'value': -principal,
            'currency': currency,
            'bnr': initial_bnr_rate,
            'value_ron': -initial_value_ron,
            'quantity': principal,
            'transactionid': f"{deposit_id} CONSTITUIRE {start_date}",
            'idx': 'ADAUGA' if prima_constituire else None,
            'storno': False
        })

        # Step 2: Calculate monthly accruals
        accrual_results = self._calculate_monthly_accruals(
            end_of_months, start_date, principal, interest_rate, convention,
            currency, deposit_id, currency_rates, initial_bnr_rate
        )
        results.extend(accrual_results['entries'])

        # Step 3: Handle maturity if deposit has ended
        if end_date <= max_date:
            maturity_results = self._calculate_maturity_entries(
                end_date, accrual_results['last_accrual_date'], principal, interest_rate,
                convention, currency, deposit_id, currency_rates, incasat,
                accrual_results['accrued_interest_cumulative'],
                accrual_results['accrued_interest_cumulative_ron'],
                accrual_results['accrued_fx_cumulative_ron'],
                accrual_results['last_bnr_rate_end'], ultima_lichidare
            )
            results.extend(maturity_results)

        # Add metadata to each entry
        for entry in results:
            entry['custodian'] = custodian
            entry['ubo'] = "DD"
            entry['partner'] = custodian
            entry['account'] = f"{custodian}_{entry['currency']}"
            entry['instrument'] = f"{custodian}_{deposit_id}"
            entry['details'] = f"{deposit_id} {entry['operation']}"

            if entry.get('storno'):
                entry['details'] = f"STORNO {entry['details']}"

            # Round values
            entry['value'] = round(entry['value'], 2)
            entry['value_ron'] = round(entry['value_ron'], 2)
            entry['quantity'] = entry.get('quantity', 0)

        return results

    def _generate_end_of_months(self, start_date, end_date):
        """Generate list of end-of-month dates between start_date and end_date"""
        from calendar import monthrange

        end_of_months = []
        current_date = start_date.replace(day=1)  # First day of start month

        while current_date <= end_date:
            # Get last day of current month
            last_day = monthrange(current_date.year, current_date.month)[1]
            end_of_month = current_date.replace(day=last_day)

            if end_of_month >= start_date:
                end_of_months.append(end_of_month)

            # Move to next month
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)

        return end_of_months

    def _get_currency_rates(self, bnr_rates: QuerySet, currency: str) -> Dict:
        """Get BNR rates for a specific currency"""
        if currency == 'RON':
            # For RON, rate is always 1
            return {rate.date: 1.0 for rate in bnr_rates}

        currency_rates = {}
        for rate in bnr_rates.filter(currency_code__currency_code=currency):
            currency_rates[rate.date] = float(rate.value_exact)

        return currency_rates

    def _get_bnr_rate_before_date(self, currency_rates: Dict, target_date) -> float:
        """Get the BNR rate for the last available date before or on target_date"""
        available_dates = [date for date in currency_rates.keys() if date <= target_date]

        if not available_dates:
            # If no rate available before target_date, use the first available rate
            if currency_rates:
                return list(currency_rates.values())[0]
            return 1.0  # Default to 1 if no rates available

        latest_date = max(available_dates)
        return currency_rates[latest_date]

    def _calculate_monthly_accruals(self, end_of_months, start_date, principal, interest_rate,
                                  convention, currency, deposit_id, currency_rates, initial_bnr_rate):
        """Calculate monthly accrual entries"""
        results = []
        accrued_interest_cumulative = 0
        accrued_interest_cumulative_ron = 0
        accrued_fx_cumulative_ron = 0
        last_accrual_date = start_date
        last_bnr_rate_end = initial_bnr_rate

        for end_of_month in end_of_months:
            # Calculate days for accrual
            days = (end_of_month - last_accrual_date).days

            # Calculate interest accrual
            interest_accrual = principal * interest_rate * days / convention
            accrued_interest_cumulative += interest_accrual

            # Get BNR rate at end of month
            bnr_rate_end = self._get_bnr_rate_before_date(currency_rates, end_of_month)

            # Calculate RON values
            interest_accrual_ron = interest_accrual * bnr_rate_end
            accrued_interest_cumulative_ron += interest_accrual_ron

            # Calculate FX difference on principal
            fx_diff_principal = principal * (bnr_rate_end - last_bnr_rate_end)
            accrued_fx_cumulative_ron += fx_diff_principal

            # Add accrual entry
            results.append({
                'date': end_of_month,
                'operation': 'ACCRUAL_DEP_VALUTA' if currency != 'RON' else 'ACCRUAL_DEP_LEI',
                'value': interest_accrual,
                'currency': currency,
                'bnr': bnr_rate_end,
                'value_ron': interest_accrual_ron,
                'quantity': 0,
                'transactionid': f"{deposit_id} ACCRUAL {end_of_month}",
                'idx': None,
                'storno': False
            })

            # Add FX difference entry if significant and currency is not RON
            if currency != 'RON' and abs(fx_diff_principal) > 0.01:
                results.append({
                    'date': end_of_month,
                    'operation': 'FX_DEP_VALUTA',
                    'value': 0,
                    'currency': currency,
                    'bnr': bnr_rate_end,
                    'value_ron': fx_diff_principal,
                    'quantity': 0,
                    'transactionid': f"{deposit_id} FX {end_of_month}",
                    'idx': None,
                    'storno': False
                })

            last_accrual_date = end_of_month
            last_bnr_rate_end = bnr_rate_end

        return {
            'entries': results,
            'accrued_interest_cumulative': accrued_interest_cumulative,
            'accrued_interest_cumulative_ron': accrued_interest_cumulative_ron,
            'accrued_fx_cumulative_ron': accrued_fx_cumulative_ron,
            'last_accrual_date': last_accrual_date,
            'last_bnr_rate_end': last_bnr_rate_end
        }

    def _calculate_maturity_entries(self, end_date, last_accrual_date, principal, interest_rate,
                                  convention, currency, deposit_id, currency_rates, incasat,
                                  accrued_interest_cumulative, accrued_interest_cumulative_ron,
                                  accrued_fx_cumulative_ron, last_bnr_rate_end, ultima_lichidare):
        """Calculate maturity entries for deposit liquidation"""
        results = []

        # Calculate final accrual if needed
        if end_date > last_accrual_date:
            days = (end_date - last_accrual_date).days
            final_interest_accrual = principal * interest_rate * days / convention
            accrued_interest_cumulative += final_interest_accrual

            # Get BNR rate at maturity
            bnr_rate_maturity = self._get_bnr_rate_before_date(currency_rates, end_date)
            final_interest_accrual_ron = final_interest_accrual * bnr_rate_maturity
            accrued_interest_cumulative_ron += final_interest_accrual_ron

            # Calculate final FX difference on principal
            fx_diff_final = principal * (bnr_rate_maturity - last_bnr_rate_end)
            accrued_fx_cumulative_ron += fx_diff_final

            # Add final accrual entry
            results.append({
                'date': end_date,
                'operation': 'ACCRUAL_DEP_VALUTA' if currency != 'RON' else 'ACCRUAL_DEP_LEI',
                'value': final_interest_accrual,
                'currency': currency,
                'bnr': bnr_rate_maturity,
                'value_ron': final_interest_accrual_ron,
                'quantity': 0,
                'transactionid': f"{deposit_id} ACCRUAL FINAL {end_date}",
                'idx': None,
                'storno': False
            })

            # Add final FX difference if significant
            if currency != 'RON' and abs(fx_diff_final) > 0.01:
                results.append({
                    'date': end_date,
                    'operation': 'FX_DEP_VALUTA',
                    'value': 0,
                    'currency': currency,
                    'bnr': bnr_rate_maturity,
                    'value_ron': fx_diff_final,
                    'quantity': 0,
                    'transactionid': f"{deposit_id} FX FINAL {end_date}",
                    'idx': None,
                    'storno': False
                })

            last_bnr_rate_end = bnr_rate_maturity

        # Calculate difference between calculated and actual interest
        interest_difference = accrued_interest_cumulative - incasat

        # Add interest collection entry
        if incasat > 0:
            results.append({
                'date': end_date,
                'operation': 'INCASARE_DOB_VALUTA' if currency != 'RON' else 'INCASARE_DOB_LEI',
                'value': incasat,
                'currency': currency,
                'bnr': last_bnr_rate_end,
                'value_ron': incasat * last_bnr_rate_end,
                'quantity': 0,
                'transactionid': f"{deposit_id} INCASARE DOBANDA {end_date}",
                'idx': None,
                'storno': False
            })

        # Add interest difference adjustment if significant
        if abs(interest_difference) > 0.01:
            results.append({
                'date': end_date,
                'operation': 'AJUSTARE_DOB_VALUTA' if currency != 'RON' else 'AJUSTARE_DOB_LEI',
                'value': -interest_difference,
                'currency': currency,
                'bnr': last_bnr_rate_end,
                'value_ron': -interest_difference * last_bnr_rate_end,
                'quantity': 0,
                'transactionid': f"{deposit_id} AJUSTARE DOBANDA {end_date}",
                'idx': None,
                'storno': False
            })

        # Add principal liquidation entry
        results.append({
            'date': end_date,
            'operation': 'LICHIDARE_DEP_VALUTA' if currency != 'RON' else 'LICHIDARE_DEP_LEI',
            'value': principal,
            'currency': currency,
            'bnr': last_bnr_rate_end,
            'value_ron': principal * last_bnr_rate_end,
            'quantity': -principal,
            'transactionid': f"{deposit_id} LICHIDARE {end_date}",
            'idx': 'STERGE' if ultima_lichidare else None,
            'storno': False
        })

        return results

    def _create_journal_entries(self, accrual_entries: List[Dict], deposit: Deposits) -> int:
        """Create or update journal entries in the database using Django ORM"""
        created_count = 0
        updated_count = 0

        for entry in accrual_entries:
            try:
                # Get or create required objects
                operation = self._get_or_create_operation(entry['operation'])
                instrument = self._get_or_create_instrument(entry['instrument'], deposit)
                account = self._get_or_create_account(entry['account'], entry['currency'])
                partner = self._get_or_create_partner(entry['partner'])
                currency = Currency.objects.get(currency_code=entry['currency'])
                custodian = Custodian.objects.get(custodian_code=entry['custodian'])

                # Prepare journal entry data
                journal_data = {
                    'date': entry['date'],
                    'operation': operation,
                    'value': Decimal(str(entry['value'])),
                    'currency': currency,
                    'bnr': Decimal(str(entry['bnr'])),
                    'value_ron': Decimal(str(entry['value_ron'])),
                    'quantity': Decimal(str(entry['quantity'])),
                    'ubo': self.ubo,
                    'custodian': custodian,
                    'account': account,
                    'partner': partner,
                    'instrument': instrument,
                    'details': entry['details'],
                    'idx': entry.get('idx'),
                    'storno': entry.get('storno', False)
                }

                # Use update_or_create with transactionid as the unique identifier
                journal, created = Journal.objects.update_or_create(
                    transactionid=entry['transactionid'],
                    defaults=journal_data
                )

                if created:
                    created_count += 1
                    logger.debug(f"Created journal entry: {journal.transactionid}")
                else:
                    updated_count += 1
                    logger.debug(f"Updated journal entry: {journal.transactionid}")

            except Exception as e:
                logger.error(f"Error creating/updating journal entry for {entry['transactionid']}: {str(e)}")
                raise

        logger.info(f"Journal entries: {created_count} created, {updated_count} updated")
        return created_count + updated_count

    def _get_or_create_operation(self, operation_code: str) -> Operation:
        """Get or create Operation object"""
        operation, created = Operation.objects.get_or_create(
            operation_code=operation_code,
            defaults={'operation_name': operation_code}
        )
        if created:
            logger.debug(f"Created new operation: {operation_code}")
        return operation

    def _get_or_create_instrument(self, instrument_symbol: str, deposit: Deposits) -> Instrument:
        """Get or create Instrument object"""
        instrument, created = Instrument.objects.get_or_create(
            symbol=instrument_symbol,
            defaults={
                'instrument_name': f"Deposit {deposit.deposit.symbol}",
                'currency': deposit.deposit.currency,
                'custodian': deposit.deposit.custodian,
                'instrument_type': 'DEPOSIT'
            }
        )
        if created:
            logger.debug(f"Created new instrument: {instrument_symbol}")
        return instrument

    def _get_or_create_account(self, account_code: str, currency_code: str) -> Account:
        """Get or create Account object"""
        account, created = Account.objects.get_or_create(
            account_code=account_code,
            defaults={
                'account_name': f"Account {account_code}",
                'currency': Currency.objects.get(currency_code=currency_code)
            }
        )
        if created:
            logger.debug(f"Created new account: {account_code}")
        return account

    def _get_or_create_partner(self, partner_code: str) -> Partner:
        """Get or create Partner object"""
        partner, created = Partner.objects.get_or_create(
            partner_code=partner_code,
            defaults={'partner_name': partner_code}
        )
        if created:
            logger.debug(f"Created new partner: {partner_code}")
        return partner

    def get_deposit_summary(self, deposit_symbol: str = None) -> Dict:
        """Get summary of deposits and their accruals"""
        deposits_queryset = self._get_deposits_queryset(deposit_symbol)

        summary = {
            'total_deposits': deposits_queryset.count(),
            'deposits': []
        }

        for deposit in deposits_queryset:
            deposit_info = self._extract_deposit_info(deposit)

            # Calculate total expected interest
            days_total = (deposit_info['end_date'] - deposit_info['start_date']).days
            expected_interest = (deposit_info['principal'] *
                               deposit_info['interest_rate'] / 100 *
                               days_total / deposit_info['convention'])

            # Get existing journal entries
            existing_journals = Journal.objects.filter(
                instrument__symbol__contains=deposit_info['deposit_id']
            ).count()

            summary['deposits'].append({
                'symbol': deposit_info['deposit_id'],
                'custodian': deposit_info['custodian'],
                'currency': deposit_info['currency'],
                'principal': deposit_info['principal'],
                'interest_rate': deposit_info['interest_rate'],
                'start_date': deposit_info['start_date'],
                'end_date': deposit_info['end_date'],
                'expected_interest': round(expected_interest, 2),
                'collected_interest': deposit_info['incasat'],
                'existing_journal_entries': existing_journals,
                'is_new': deposit_info['prima_constituire'],
                'is_liquidated': deposit_info['ultima_lichidare']
            })

        return summary
