<h1 align="center">TypeScript API Utils</h1>

<p align="center">
	Utility functions for working with TypeScript's API.
	Successor to the wonderful <a href="https://github.com/ajafff/tsutils">tsutils</a>.
</p>

<p align="center">
	<a href="#contributors" target="_blank">
<!-- prettier-ignore-start -->
<!-- ALL-CONTRIBUTORS-BADGE:START - Do not remove or modify this section -->
<img alt="All Contributors: 4" src="https://img.shields.io/badge/all_contributors-4-21bb42.svg" />
<!-- ALL-CONTRIBUTORS-BADGE:END -->
<!-- prettier-ignore-end -->
	</a>
	<a href="https://codecov.io/gh/JoshuaKGoldberg/ts-api-utils" target="_blank">
		<img alt="Codecov Test Coverage" src="https://codecov.io/gh/JoshuaKGoldberg/ts-api-utils/branch/main/graph/badge.svg?token=eVIFY4MhfQ"/>
	</a>
	<a href="#" target="_blank">
		<img alt="Documentation Coverage" src="./docs/coverage.svg"/>
	</a>
	<a href="https://github.com/JoshuaKGoldberg/ts-api-utils/blob/main/.github/CODE_OF_CONDUCT.md" target="_blank">
		<img alt="Contributor Covenant" src="https://img.shields.io/badge/code_of_conduct-enforced-21bb42" />
	</a>
	<a href="https://github.com/JoshuaKGoldberg/ts-api-utils/blob/main/LICENSE.md" target="_blank">
	    <img alt="License: MIT" src="https://img.shields.io/github/license/JoshuaKGoldberg/ts-api-utils?color=21bb42">
    </a>
	<a href="https://github.com/sponsors/JoshuaKGoldberg" target="_blank">
    	<img alt="Sponsor: On GitHub" src="https://img.shields.io/badge/sponsor-on_github-21bb42.svg" />
    </a>
	<img alt="Style: Prettier" src="https://img.shields.io/badge/style-prettier-21bb42.svg" />
    <img alt="TypeScript: Strict" src="https://img.shields.io/badge/typescript-strict-21bb42.svg" />
</p>

## Usage

```shell
npm i ts-api-utils
```

```ts
import * as tsutils from "ts-api-utils";
```

### API

`ts-api-utils` provides many utility functions.
Check out our API docs for details:

📝 [ts-api-utils API docs](https://joshuakgoldberg.github.io/ts-api-utils).

## Development

See [`.github/CONTRIBUTING.md`](./.github/CONTRIBUTING.md).
Thanks! 💖

## Contributors

Many thanks to [@ajafff](https://github.com/ajafff) for creating the original [`tsutils`](https://github.com/ajafff/tsutils) ([original license: MIT](https://github.com/ajafff/tsutils/blob/26b195358ec36d59f00333115aa3ffd9611ca78b/LICENSE)) that this project was originally based on! 🙏

<!-- prettier-ignore-start -->
<!-- markdownlint-disable -->
<!-- spellchecker: disable -->
<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->
<!-- prettier-ignore-start -->
<!-- markdownlint-disable -->
<table>
  <tbody>
    <tr>
      <td align="center" valign="top" width="14.28%"><a href="http://www.joshuakgoldberg.com"><img src="https://avatars.githubusercontent.com/u/3335181?v=4?s=100" width="100px;" alt="Josh Goldberg"/><br /><sub><b>Josh Goldberg</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/issues?q=author%3AJoshuaKGoldberg" title="Bug reports">🐛</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=JoshuaKGoldberg" title="Code">💻</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=JoshuaKGoldberg" title="Documentation">📖</a> <a href="#projectManagement-JoshuaKGoldberg" title="Project Management">📆</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=JoshuaKGoldberg" title="Tests">⚠️</a> <a href="#tool-JoshuaKGoldberg" title="Tools">🔧</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/RebeccaStevens"><img src="https://avatars.githubusercontent.com/u/7224206?v=4?s=100" width="100px;" alt="Rebecca Stevens"/><br /><sub><b>Rebecca Stevens</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/issues?q=author%3ARebeccaStevens" title="Bug reports">🐛</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=RebeccaStevens" title="Code">💻</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=RebeccaStevens" title="Documentation">📖</a> <a href="#projectManagement-RebeccaStevens" title="Project Management">📆</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=RebeccaStevens" title="Tests">⚠️</a> <a href="#tool-RebeccaStevens" title="Tools">🔧</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://github.com/ajafff"><img src="https://avatars.githubusercontent.com/u/11968040?v=4?s=100" width="100px;" alt="Klaus Meinhardt"/><br /><sub><b>Klaus Meinhardt</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=ajafff" title="Code">💻</a> <a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=ajafff" title="Tests">⚠️</a></td>
      <td align="center" valign="top" width="14.28%"><a href="https://blog.jmchor.dev"><img src="https://avatars.githubusercontent.com/u/110151013?v=4?s=100" width="100px;" alt="Johannes Chorzempa"/><br /><sub><b>Johannes Chorzempa</b></sub></a><br /><a href="https://github.com/JoshuaKGoldberg/ts-api-utils/commits?author=jmchor" title="Documentation">📖</a></td>
    </tr>
  </tbody>
</table>

<!-- markdownlint-restore -->
<!-- prettier-ignore-end -->

<!-- ALL-CONTRIBUTORS-LIST:END -->
<!-- spellchecker: enable -->
<!-- markdownlint-restore -->
<!-- prettier-ignore-end -->

> 💙 This package is templated from [@JoshuaKGoldberg](https://github.com/JoshuaKGoldberg)'s [template-typescript-node-package](https://github.com/JoshuaKGoldberg/template-typescript-node-package).

> _"My tools! I have to have my tools!" - Dennis Reynolds_
