# nch

# Flux curent
1. Se descarca si importa automat cursul BNR folosind curs_bnr (cron job)
2. Se descarca zilnic toate rapoartele IBKR si Tradeville folosind api_ibkr si api_tdv (cron job)
3. [functioneaza dar necesita refacere] Se importa automat portofoliul IBKR si TDV folosind upload_ibkr_port si upload_tdv_port in baza de date pentru raportare folosind Apache Superset. Proiectul trebuie regandit conform fluxului principal.
4. Se pregateste formatul de import in Journal folosind uptrz_ibkr si uptrz_tdv. De principiu comenzile ar trebui sa si incarce rezultatul in baza de date dar in practica au tot aparut diverse situatii de tratat si fisierele se verifica manual si tot manual se importa. S-ar putea rezolva cu import automat preliminar urmat de o validare manuala linie cu linie.
5. Import manual situatie depozite bancare
6. Introdus manual instrumente noi
7. Import rapoarte banci si brokeri (conform punctul anterior pentru brokeri) folosind uptrz_manual. Formatul de import este in fisierul Journal atasat (am scos cifrele). Acest format se si poate exporta din baza de date, vezi port/admin
8. Calculat accruals si diferente de curs valutar depozite folosind port/deposit_accruals
9. Calculat accruals bonds si diferente de curs valutar folosind port/bond_accruals_ql (mai e acolo bond_accruals insa algoritmul producea mult mai multe erori decat cel cu Quantlib)
10. Verificat cursurile BNR folosind fix_bnr_rates
11. Calculat o serie de alte diferente de curs cerute de contabil dif_fx
12. Export jurnal format contabilitate (model Journal contabiliate) folosing codul din port/admin
 
# Refactoring:
1. Ajustat codul la best practices (refactoring). Inclusiv ajustat codul specifice IBKR si TDV ca sa mearga importurile automat.
2. Operatiile de import/export trebuie transferate la cineva din backoffice care sa le opereze de 2-3 ori pe saptamana, momentan le fac eu lunar ca sa si ajustez codul unde sunt erori.
3. Adaugat validari (post-calcule) pentru accruals si diferente de curs pentru identificarea erorilor din jurnal.
4. Trebuie introduce o validare a calculelor din aplicatie vs calculi brokeri cu semnalarea diferentelor. [ulterior] Corectat formulele de accruals bonds unde nu corespund rezultate cu brokerii – cred ca nu folosesc 100% corect QuantLib, biblioteca e extrem de complexa.

# New features:
1. Adaugat integrare API Libra Bank.
2. Incarcare automata rapoarte IBKR TDV (dezvoltare facuta si functionala dar utlizarea suspendata pana la o varianta fiabila) Libra
3. Rapoarte catre management - direct in Django (standardizate) si/sau Superset (customizabile de utlizatori)
4. **AI Hedge Fund Backend** - Integrated FastAPI backend for AI-powered hedge fund operations


# Running a worker manually:
celery -A nch call port.tasks.fetch_bnr_rates
celery -A nch call port.tasks.fetch_ibkr_data
docker-compose exec celery_worker celery -A nch call port.tasks.fetch_ibkr_data


# Development Setup
1. Copy `.env.example` to `.env` and fill in the development variables
2. Run: `docker-compose --profile development build`
3. Run: `docker-compose --profile development up`
or
2. Run: `docker-compose --profile development up --build`

## Services Available:
- **Main Django App**: http://localhost:8002
- **Frontend**: http://localhost:8082
- **AI Hedge Fund Backend API**: http://localhost:8001
- **AI Hedge Fund API Docs**: http://localhost:8001/docs
- **Flower (Celery Monitor)**: http://localhost:5557
- **PostgreSQL**: localhost:5434
- **Redis**: localhost:6381

# Production Setup
1. Copy `.env.example` to `.env` and fill in the production variables
2. Run: `docker-compose -f docker-compose.prod.yml build`
3. Run: `docker-compose -f docker-compose.prod.yml up`
