from rest_framework import serializers
from .models import AIHedgeFundCreds


class AIHedgeFundCredsSerializer(serializers.ModelSerializer):
    """Serializer for creating/updating AI Hedge Fund credentials"""
    
    class Meta:
        model = AIHedgeFundCreds
        fields = [
            'id', 'name', 'description', 'is_active',
            'anthropic_api_key', 'deepseek_api_key', 'groq_api_key',
            'google_api_key', 'openai_api_key', 'financial_datasets_api_key',
            'openai_api_base', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate(self, data):
        """Ensure at least one API key is provided"""
        api_keys = [
            data.get('anthropic_api_key'),
            data.get('deepseek_api_key'),
            data.get('groq_api_key'),
            data.get('google_api_key'),
            data.get('openai_api_key')
        ]
        
        if not any(api_keys):
            raise serializers.ValidationError(
                "At least one AI provider API key must be provided."
            )
        
        return data


class AIHedgeFundCredsReadSerializer(serializers.ModelSerializer):
    """Serializer for reading AI Hedge Fund credentials (with masked sensitive data)"""
    
    anthropic_api_key = serializers.SerializerMethodField()
    deepseek_api_key = serializers.SerializerMethodField()
    groq_api_key = serializers.SerializerMethodField()
    google_api_key = serializers.SerializerMethodField()
    openai_api_key = serializers.SerializerMethodField()
    financial_datasets_api_key = serializers.SerializerMethodField()
    available_providers = serializers.SerializerMethodField()
    has_financial_data = serializers.SerializerMethodField()
    
    class Meta:
        model = AIHedgeFundCreds
        fields = [
            'id', 'name', 'description', 'is_active',
            'anthropic_api_key', 'deepseek_api_key', 'groq_api_key',
            'google_api_key', 'openai_api_key', 'financial_datasets_api_key',
            'openai_api_base', 'available_providers', 'has_financial_data',
            'created_at', 'updated_at'
        ]
    
    def _mask_api_key(self, api_key):
        """Mask API key for security (show only first 4 and last 4 characters)"""
        if not api_key:
            return None
        if len(api_key) <= 8:
            return "****"
        return f"{api_key[:4]}{'*' * (len(api_key) - 8)}{api_key[-4:]}"
    
    def get_anthropic_api_key(self, obj):
        return self._mask_api_key(obj.anthropic_api_key)
    
    def get_deepseek_api_key(self, obj):
        return self._mask_api_key(obj.deepseek_api_key)
    
    def get_groq_api_key(self, obj):
        return self._mask_api_key(obj.groq_api_key)
    
    def get_google_api_key(self, obj):
        return self._mask_api_key(obj.google_api_key)
    
    def get_openai_api_key(self, obj):
        return self._mask_api_key(obj.openai_api_key)
    
    def get_financial_datasets_api_key(self, obj):
        return self._mask_api_key(obj.financial_datasets_api_key)
    
    def get_available_providers(self, obj):
        return obj.get_available_providers()
    
    def get_has_financial_data(self, obj):
        return obj.has_financial_data_access()


class AIHedgeFundCredsExportSerializer(serializers.ModelSerializer):
    """Serializer for exporting credentials as environment variables"""
    
    class Meta:
        model = AIHedgeFundCreds
        fields = [
            'anthropic_api_key', 'deepseek_api_key', 'groq_api_key',
            'google_api_key', 'openai_api_key', 'financial_datasets_api_key',
            'openai_api_base'
        ]
    
    def to_representation(self, instance):
        """Convert to environment variable format"""
        data = super().to_representation(instance)
        env_vars = {}
        
        if data.get('anthropic_api_key'):
            env_vars['ANTHROPIC_API_KEY'] = data['anthropic_api_key']
        if data.get('deepseek_api_key'):
            env_vars['DEEPSEEK_API_KEY'] = data['deepseek_api_key']
        if data.get('groq_api_key'):
            env_vars['GROQ_API_KEY'] = data['groq_api_key']
        if data.get('google_api_key'):
            env_vars['GOOGLE_API_KEY'] = data['google_api_key']
        if data.get('openai_api_key'):
            env_vars['OPENAI_API_KEY'] = data['openai_api_key']
        if data.get('financial_datasets_api_key'):
            env_vars['FINANCIAL_DATASETS_API_KEY'] = data['financial_datasets_api_key']
        if data.get('openai_api_base'):
            env_vars['OPENAI_API_BASE'] = data['openai_api_base']
        
        return env_vars
