import { BACKEND_URL } from "./constants.js";
import { showToast } from "./utils.js";

const ibkrTable = document.querySelector("#ibkrTable tbody");
const tdvTable = document.querySelector("#tdvTable tbody");
const aiHedgeFundTable = document.querySelector("#aiHedgeFundTable tbody");
const addBtn = document.getElementById("addCredentialBtn");
const form = document.getElementById("credentialForm");
const modalEl = document.getElementById("credentialModal");
const modal = new bootstrap.Modal(modalEl);

const idInput = document.getElementById("credentialId");
const typeInput = document.getElementById("credentialType");

const ibkrTokenInput = document.getElementById("ibkrToken");
const ibkrQueryInput = document.getElementById("ibkrQuery");
const ibkrDailyInput = document.getElementById("ibkrQueryDaily");

const tdvUserInput = document.getElementById("tdvUser");
const tdvPassInput = document.getElementById("tdvPassword");
const tdvDdInput = document.getElementById("tdvDd");

// AI Hedge Fund inputs
const aiNameInput = document.getElementById("aiName");
const aiDescriptionInput = document.getElementById("aiDescription");
const anthropicApiKeyInput = document.getElementById("anthropicApiKey");
const openaiApiKeyInput = document.getElementById("openaiApiKey");
const groqApiKeyInput = document.getElementById("groqApiKey");
const googleApiKeyInput = document.getElementById("googleApiKey");
const deepseekApiKeyInput = document.getElementById("deepseekApiKey");
const financialDatasetsApiKeyInput = document.getElementById("financialDatasetsApiKey");
const openaiApiBaseInput = document.getElementById("openaiApiBase");
const aiIsActiveInput = document.getElementById("aiIsActive");

let editing = false;

function createRow(cred, type) {
  const tr = document.createElement("tr");
  let name = "";
  let extraColumn = "";

  if (type === "ibkr") {
    name = cred.masked_token;
  } else if (type === "tdv") {
    name = cred.masked_user || "Utilizator";
  } else if (type === "ai_hedge_fund") {
    name = cred.name || "AI Credentials";
    extraColumn = `<td>${cred.available_providers ? cred.available_providers.join(', ') : 'None'}</td>`;
  }

  const baseColumns = `
    <td>${cred.id}</td>
    <td>${name}</td>
    ${extraColumn}
    <td>
      <button class="btn btn-sm btn-primary me-1" data-id="${cred.id}" data-type="${type}" data-action="edit">Editează</button>
      <button class="btn btn-sm btn-danger" data-id="${cred.id}" data-type="${type}" data-action="delete">Șterge</button>
      ${type === 'ai_hedge_fund' ? `<button class="btn btn-sm btn-success" data-id="${cred.id}" data-type="${type}" data-action="export">Export .env</button>` : ''}
    </td>
  `;

  tr.innerHTML = baseColumns;
  return tr;
}

function updateRequiredFields(type) {
  const allInputs = [
    ibkrTokenInput, ibkrQueryInput, ibkrDailyInput,
    tdvUserInput, tdvPassInput, tdvDdInput,
    aiNameInput
  ];
  allInputs.forEach(input => input.removeAttribute("required"));

  if (type === "ibkr") {
    ibkrTokenInput.required = true;
    ibkrQueryInput.required = true;
    ibkrDailyInput.required = true;
  } else if (type === "tdv") {
    tdvUserInput.required = true;
    tdvPassInput.required = true;
    tdvDdInput.required = true;
  } else if (type === "ai_hedge_fund") {
    aiNameInput.required = true;
  }
}

async function loadCredentials() {
  try {
    const token = localStorage.getItem("accessToken");
    const res = await fetch(`${BACKEND_URL}/credentials/`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    if (!res.ok) throw new Error("Eroare la încărcarea credențialelor.");
    const data = await res.json();

    ibkrTable.innerHTML = "";
    tdvTable.innerHTML = "";
    aiHedgeFundTable.innerHTML = "";
    data.ibkr_credentials.forEach(cred => ibkrTable.appendChild(createRow(cred, "ibkr")));
    data.tdv_credentials.forEach(cred => tdvTable.appendChild(createRow(cred, "tdv")));
    if (data.ai_hedge_fund_credentials) {
      data.ai_hedge_fund_credentials.forEach(cred => aiHedgeFundTable.appendChild(createRow(cred, "ai_hedge_fund")));
    }
  } catch (err) {
    console.error(err);
    showToast("Eroare la încărcare credențiale.", "danger");
  }
}

function resetForm() {
  editing = false;
  idInput.value = "";
  typeInput.value = "ibkr";

  ibkrTokenInput.value = "";
  ibkrQueryInput.value = "";
  ibkrDailyInput.value = "";

  tdvUserInput.value = "";
  tdvPassInput.value = "";
  tdvDdInput.value = "";

  // Reset AI Hedge Fund fields
  aiNameInput.value = "";
  aiDescriptionInput.value = "";
  anthropicApiKeyInput.value = "";
  openaiApiKeyInput.value = "";
  groqApiKeyInput.value = "";
  googleApiKeyInput.value = "";
  deepseekApiKeyInput.value = "";
  financialDatasetsApiKeyInput.value = "";
  openaiApiBaseInput.value = "";
  aiIsActiveInput.checked = true;

  document.querySelectorAll(".type-group").forEach(el => el.classList.add("d-none"));
  document.querySelector("#ibkrFields").classList.remove("d-none");
  updateRequiredFields("ibkr");
}

addBtn.addEventListener("click", () => {
  resetForm();
  modalEl.querySelector(".modal-title").textContent = "Adaugă credențiale";
  modal.show();
});

typeInput.addEventListener("change", () => {
  document.querySelectorAll(".type-group").forEach(el => el.classList.add("d-none"));
  if (typeInput.value === "ibkr") document.querySelector("#ibkrFields").classList.remove("d-none");
  else if (typeInput.value === "tdv") document.querySelector("#tdvFields").classList.remove("d-none");
  else if (typeInput.value === "ai_hedge_fund") document.querySelector("#aiHedgeFundFields").classList.remove("d-none");
  updateRequiredFields(typeInput.value);
});

form.addEventListener("submit", async (e) => {
  e.preventDefault();
  const id = idInput.value;
  const type = typeInput.value;
  const token = localStorage.getItem("accessToken");

  let payload = { id, type };
  if (type === "ibkr") {
    payload.ibkr_token = ibkrTokenInput.value;
    payload.ibkr_query = ibkrQueryInput.value;
    payload.ibkr_query_daily = ibkrDailyInput.value;
  } else if (type === "tdv") {
    payload.tdv_user = tdvUserInput.value;
    payload.tdv_password = tdvPassInput.value;
    payload.tdv_dd = tdvDdInput.value;
  } else if (type === "ai_hedge_fund") {
    payload.name = aiNameInput.value;
    payload.description = aiDescriptionInput.value;
    payload.is_active = aiIsActiveInput.checked;

    // Only include API keys that have values
    if (anthropicApiKeyInput.value) payload.anthropic_api_key = anthropicApiKeyInput.value;
    if (openaiApiKeyInput.value) payload.openai_api_key = openaiApiKeyInput.value;
    if (groqApiKeyInput.value) payload.groq_api_key = groqApiKeyInput.value;
    if (googleApiKeyInput.value) payload.google_api_key = googleApiKeyInput.value;
    if (deepseekApiKeyInput.value) payload.deepseek_api_key = deepseekApiKeyInput.value;
    if (financialDatasetsApiKeyInput.value) payload.financial_datasets_api_key = financialDatasetsApiKeyInput.value;
    if (openaiApiBaseInput.value) payload.openai_api_base = openaiApiBaseInput.value;
  }

  try {
    const res = await fetch(`${BACKEND_URL}/credentials/`, {
      method: editing ? "PUT" : "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify(payload)
    });

    if (!res.ok) {
      const err = await res.json();
      throw new Error(err.error || "Eroare necunoscută.");
    }

    modal.hide();
    showToast(editing ? "Credențială actualizată." : "Credențială creată.", "success");
    await loadCredentials();
  } catch (err) {
    console.error(err);
    showToast(err.message, "danger");
  }
});

document.addEventListener("click", async (e) => {
  if (e.target.dataset.action === "edit") {
    editing = true;
    const id = e.target.dataset.id;
    const type = e.target.dataset.type;

    idInput.value = id;
    typeInput.value = type;
    typeInput.dispatchEvent(new Event("change"));

    if (type === "ibkr") {
      ibkrTokenInput.value = "";
      ibkrQueryInput.value = "";
      ibkrDailyInput.value = "";
    } else if (type === "tdv") {
      tdvUserInput.value = "";
      tdvPassInput.value = "";
      tdvDdInput.value = "";
    } else if (type === "ai_hedge_fund") {
      // For AI hedge fund, we'll leave fields empty for security
      // User will need to re-enter sensitive data
      aiNameInput.value = "";
      aiDescriptionInput.value = "";
      aiIsActiveInput.checked = true;
    }

    modalEl.querySelector(".modal-title").textContent = "Editează credențiale";
    modal.show();
  }

  if (e.target.dataset.action === "delete") {
    const confirmed = confirm("Sigur dorești să ștergi această credențială?");
    if (!confirmed) return;

    const token = localStorage.getItem("accessToken");
    try {
      const res = await fetch(`${BACKEND_URL}/credentials/`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({ id: e.target.dataset.id, type: e.target.dataset.type })
      });
      if (!res.ok) {
        const err = await res.json();
        throw new Error(err.error || "Eroare la ștergere.");
      }
      showToast("Credențială ștearsă.", "success");
      await loadCredentials();
    } catch (err) {
      console.error(err);
      showToast(err.message, "danger");
    }
  }

  // Handle export action for AI hedge fund credentials
  if (e.target.dataset.action === "export") {
    const id = e.target.dataset.id;
    const type = e.target.dataset.type;

    if (type === "ai_hedge_fund") {
      try {
        const token = localStorage.getItem("accessToken");
        const res = await fetch(`${BACKEND_URL}/ai-hedge-fund/ai-hedge-fund-creds/export/${id}/`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (!res.ok) {
          const err = await res.json();
          throw new Error(err.error || "Eroare la export.");
        }

        // Download the .env file
        const blob = await res.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `ai_hedge_fund_credentials_${id}.env`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        showToast("Fișier .env descărcat cu succes!", "success");
      } catch (err) {
        console.error(err);
        showToast(err.message, "danger");
      }
    }
  }
});

// Initialize
loadCredentials();
