import { AI_HEDGE_FUND_URL } from './constants.js';

class AIHedgeFund {
    constructor() {
        this.eventSource = null;
        this.isRunning = false;
        this.agents = [];
        this.models = [];
        this.init();
    }

    async init() {
        this.setupEventListeners();
        this.setDefaultDates();
        await this.loadAgents();
        await this.loadModels();
    }

    setupEventListeners() {
        const form = document.getElementById('hedgeFundForm');
        const stopBtn = document.getElementById('stopAnalysisBtn');
        const modelProvider = document.getElementById('modelProvider');

        form.addEventListener('submit', (e) => this.handleSubmit(e));
        stopBtn.addEventListener('click', () => this.stopAnalysis());
        modelProvider.addEventListener('change', () => this.updateModelOptions());
    }

    setDefaultDates() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 90); // 90 days ago

        document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
        document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
    }

    async loadAgents() {
        console.log("GETTING AGENTS")
        try {
            const response = await fetch(`${AI_HEDGE_FUND_URL}/hedge-fund/agents`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            console.log("RESPONSE:")
            console.log(response)
            const data = await response.json();
            this.agents = data.agents || [];
            this.renderAgents();
        } catch (error) {
            console.error('Failed to load agents:', error);
            this.showError('Failed to load available agents. Make sure AI Hedge Fund backend is running.');
        }
    }

    async loadModels() {
        try {
            const response = await fetch(`${AI_HEDGE_FUND_URL}/hedge-fund/language-models`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            this.models = data.models || [];
            this.updateModelOptions();
        } catch (error) {
            console.error('Failed to load models:', error);
            this.showError('Failed to load available models. Make sure AI Hedge Fund backend is running.');
        }
    }

    renderAgents() {
        const container = document.getElementById('agentsContainer');
        
        if (this.agents.length === 0) {
            container.innerHTML = '<div class="text-muted">No agents available</div>';
            return;
        }

        const agentsHtml = this.agents.map(agent => `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="${agent.id}" 
                       id="agent_${agent.id}" checked>
                <label class="form-check-label" for="agent_${agent.id}">
                    <strong>${agent.name}</strong>
                    <br><small class="text-muted">${agent.description}</small>
                </label>
            </div>
        `).join('');

        container.innerHTML = agentsHtml;
    }

    updateModelOptions() {
        const provider = document.getElementById('modelProvider').value;
        const modelSelect = document.getElementById('modelName');
        
        // Filter models by provider
        const providerModels = this.models.filter(model => 
            model.provider.toUpperCase() === provider
        );

        modelSelect.innerHTML = providerModels.map(model => 
            `<option value="${model.name}">${model.display_name || model.name}</option>`
        ).join('');
    }

    async handleSubmit(e) {
        e.preventDefault();
        
        if (this.isRunning) {
            this.showError('Analysis is already running');
            return;
        }

        const formData = this.getFormData();
        if (!this.validateFormData(formData)) {
            return;
        }

        this.startAnalysis(formData);
    }

    getFormData() {
        const selectedAgents = Array.from(
            document.querySelectorAll('#agentsContainer input[type="checkbox"]:checked')
        ).map(cb => cb.value);

        return {
            tickers: document.getElementById('tickers').value.split(',').map(t => t.trim()),
            selected_agents: selectedAgents,
            start_date: document.getElementById('startDate').value,
            end_date: document.getElementById('endDate').value,
            model_name: document.getElementById('modelName').value,
            model_provider: document.getElementById('modelProvider').value,
            initial_cash: parseFloat(document.getElementById('initialCash').value),
            margin_requirement: 0.0
        };
    }

    validateFormData(data) {
        if (data.tickers.length === 0 || data.tickers[0] === '') {
            this.showError('Please enter at least one stock ticker');
            return false;
        }

        if (data.selected_agents.length === 0) {
            this.showError('Please select at least one AI agent');
            return false;
        }

        if (data.initial_cash < 1000) {
            this.showError('Initial cash must be at least $1,000');
            return false;
        }

        return true;
    }

    async startAnalysis(formData) {
        this.isRunning = true;
        this.updateUI(true);
        this.clearResults();
        this.showProgress();

        try {
            const response = await fetch(`${AI_HEDGE_FUND_URL}/hedge-fund/run`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            this.setupEventStream(response);
        } catch (error) {
            console.error('Failed to start analysis:', error);
            this.showError('Failed to start analysis: ' + error.message);
            this.stopAnalysis();
        }
    }

    setupEventStream(response) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        const readStream = async () => {
            try {
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                this.handleStreamEvent(data);
                            } catch (e) {
                                console.error('Failed to parse event data:', e);
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('Stream reading error:', error);
                this.showError('Connection lost during analysis');
            } finally {
                this.stopAnalysis();
            }
        };

        readStream();
    }

    handleStreamEvent(event) {
        switch (event.type) {
            case 'start':
                this.updateStatus('Analysis started', 'info');
                break;
            case 'progress':
                this.addProgressUpdate(event);
                break;
            case 'complete':
                this.showResults(event.data);
                this.updateStatus('Analysis completed successfully', 'success');
                break;
            case 'error':
                this.showError(event.message);
                break;
        }
    }

    addProgressUpdate(event) {
        const container = document.getElementById('progressContainer');
        const timestamp = new Date(event.timestamp).toLocaleTimeString();
        
        const progressHtml = `
            <div class="progress-item mb-2 p-2 border-start border-3 border-primary">
                <div class="d-flex justify-content-between">
                    <strong>${event.agent}</strong>
                    <small class="text-muted">${timestamp}</small>
                </div>
                <div class="text-muted">${event.ticker}: ${event.status}</div>
                ${event.analysis ? `<div class="mt-1"><small>${event.analysis}</small></div>` : ''}
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', progressHtml);
        container.scrollTop = container.scrollHeight;
    }

    showResults(data) {
        document.getElementById('resultsPanel').style.display = 'block';
        
        // Display decisions
        const decisionsContainer = document.getElementById('decisionsContainer');
        decisionsContainer.innerHTML = this.formatDecisions(data.decisions);
        
        // Display signals
        const signalsContainer = document.getElementById('signalsContainer');
        signalsContainer.innerHTML = this.formatSignals(data.analyst_signals);
    }

    formatDecisions(decisions) {
        if (!decisions || Object.keys(decisions).length === 0) {
            return '<div class="text-muted">No investment decisions available</div>';
        }

        return Object.entries(decisions).map(([ticker, decision]) => `
            <div class="decision-item mb-3 p-3 border rounded">
                <h6 class="text-primary">${ticker}</h6>
                <div class="row">
                    <div class="col-6">
                        <strong>Action:</strong> 
                        <span class="badge ${this.getActionBadgeClass(decision.action)}">${decision.action}</span>
                    </div>
                    <div class="col-6">
                        <strong>Quantity:</strong> ${decision.quantity || 'N/A'}
                    </div>
                </div>
                ${decision.reasoning ? `<div class="mt-2"><small>${decision.reasoning}</small></div>` : ''}
            </div>
        `).join('');
    }

    formatSignals(signals) {
        if (!signals || Object.keys(signals).length === 0) {
            return '<div class="text-muted">No analyst signals available</div>';
        }

        return Object.entries(signals).map(([agent, agentSignals]) => `
            <div class="signal-item mb-3 p-3 border rounded">
                <h6 class="text-info">${agent}</h6>
                ${Object.entries(agentSignals).map(([ticker, signal]) => `
                    <div class="mb-2">
                        <strong>${ticker}:</strong> 
                        <span class="badge ${this.getSignalBadgeClass(signal.signal)}">${signal.signal}</span>
                        ${signal.confidence ? `<small class="text-muted">(${signal.confidence}% confidence)</small>` : ''}
                    </div>
                `).join('')}
            </div>
        `).join('');
    }

    getActionBadgeClass(action) {
        switch (action?.toLowerCase()) {
            case 'buy': return 'bg-success';
            case 'sell': return 'bg-danger';
            case 'hold': return 'bg-warning';
            default: return 'bg-secondary';
        }
    }

    getSignalBadgeClass(signal) {
        switch (signal?.toLowerCase()) {
            case 'bullish': return 'bg-success';
            case 'bearish': return 'bg-danger';
            case 'neutral': return 'bg-warning';
            default: return 'bg-secondary';
        }
    }

    stopAnalysis() {
        this.isRunning = false;
        this.updateUI(false);
        
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    updateUI(running) {
        document.getElementById('runAnalysisBtn').disabled = running;
        document.getElementById('stopAnalysisBtn').disabled = !running;
        
        if (running) {
            document.getElementById('runAnalysisBtn').innerHTML = 
                '<span class="spinner-border spinner-border-sm me-2"></span>Running...';
        } else {
            document.getElementById('runAnalysisBtn').innerHTML = 
                '<i class="fas fa-play me-2"></i>Run Analysis';
        }
    }

    showProgress() {
        document.getElementById('progressPanel').style.display = 'block';
        document.getElementById('progressContainer').innerHTML = '';
    }

    clearResults() {
        document.getElementById('resultsPanel').style.display = 'none';
        document.getElementById('decisionsContainer').innerHTML = '';
        document.getElementById('signalsContainer').innerHTML = '';
    }

    updateStatus(message, type = 'info') {
        const container = document.getElementById('statusContainer');
        const iconClass = type === 'success' ? 'fa-check-circle' : 
                         type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle';
        const textClass = type === 'success' ? 'text-success' : 
                         type === 'error' ? 'text-danger' : 'text-info';
        
        container.innerHTML = `
            <div class="text-center ${textClass}">
                <i class="fas ${iconClass} fa-2x mb-2"></i>
                <p>${message}</p>
            </div>
        `;
    }

    showError(message) {
        this.updateStatus(message, 'error');
        
        // Also show a toast notification
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-danger border-0';
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        toast.addEventListener('hidden.bs.toast', () => {
            document.body.removeChild(toast);
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AIHedgeFund();
});
