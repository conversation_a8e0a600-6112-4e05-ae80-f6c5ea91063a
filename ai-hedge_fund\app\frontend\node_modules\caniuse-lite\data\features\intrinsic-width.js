module.exports={A:{A:{"2":"K D E F A B oC"},B:{"2":"C L M G N O P","1025":"0 9 d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB I QB","1537":"Q H R S T U V W X Y Z a b c"},C:{"2":"pC","932":"1 2 3 4 5 6 7 8 NC J RB K D E F A B C L M G N O P SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB OC yB PC zB 0B 1B 2B sC tC","2308":"0 9 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC Q H R QC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB I QB GC RC SC qC rC"},D:{"2":"1 2 J RB K D E F A B C L M G N O P SB","545":"3 4 5 6 7 8 TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB","1025":"0 9 d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB I QB GC RC SC","1537":"lB mB nB oB pB qB rB sB tB uB vB wB xB OC yB PC zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC Q H R S T U V W X Y Z a b c"},E:{"1":"KC XC YC ZC aC bC 3C LC cC dC eC fC gC 4C MC hC iC jC kC lC 5C","2":"J RB K uC TC vC","516":"B C L M G HC IC zC 0C 1C VC WC JC 2C","548":"F A yC UC","676":"D E wC xC"},F:{"2":"F B C 6C 7C 8C 9C HC mC AD IC","513":"ZB","545":"1 2 3 4 5 6 7 8 G N O P SB TB UB VB WB XB","1025":"0 e f g h i j k l m n o p q r s t u v w x y z","1537":"YB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC FC Q H R QC S T U V W X Y Z a b c d"},G:{"1":"KC XC YC ZC aC bC WD LC cC dC eC fC gC XD MC hC iC jC kC lC","2":"TC BD nC CD DD","516":"SD TD UD VC WC JC VD","548":"GD HD ID JD KD LD MD ND OD PD QD RD","676":"E ED FD"},H:{"2":"YD"},I:{"2":"NC J ZD aD bD cD nC","545":"dD eD","1025":"I"},J:{"2":"D","545":"A"},K:{"2":"A B C HC mC IC","1025":"H"},L:{"1025":"I"},M:{"2308":"GC"},N:{"2":"A B"},O:{"1537":"JC"},P:{"545":"J","1025":"1 2 3 4 5 6 7 8 LC MC pD","1537":"fD gD hD iD jD UC kD lD mD nD oD KC"},Q:{"1537":"qD"},R:{"1537":"rD"},S:{"932":"sD","2308":"tD"}},B:5,C:"Intrinsic & Extrinsic Sizing",D:true};
