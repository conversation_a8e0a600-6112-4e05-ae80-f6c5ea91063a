{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from \"chalk\"\n\nfunction getInvoker() {\n  const args = process.argv.slice(2)\n  const env = process.env\n  const npmExecPath = env.npm_execpath || \"\"\n  const packageName = \"shadcn@latest\"\n\n  if (npmExecPath.includes(\"pnpm\")) {\n    return `pnpm dlx ${packageName}${args.length ? ` ${args.join(\" \")}` : \"\"}`\n  } else if (npmExecPath.includes(\"yarn\")) {\n    return `yarn dlx ${packageName}${args.length ? ` ${args.join(\" \")}` : \"\"}`\n  } else if (npmExecPath.includes(\"bun\")) {\n    return `bunx ${packageName}${args.length ? ` ${args.join(\" \")}` : \"\"}`\n  } else {\n    return `npx ${packageName}${args.length ? ` ${args.join(\" \")}` : \"\"}`\n  }\n}\n\nconst main = async () => {\n  console.log(\n    chalk.yellow(\n      \"The 'shadcn-ui' package is deprecated. Please use the 'shadcn' package instead:\"\n    )\n  )\n  console.log(\"\")\n  console.log(chalk.green(`  ${getInvoker()}`))\n  console.log(\"\")\n  console.log(\n    chalk.yellow(\"For more information, visit: https://ui.shadcn.com/docs/cli\")\n  )\n  console.log(\"\")\n}\n\nmain().catch((error) => {\n  console.error(chalk.red(\"Error:\"), error.message)\n  process.exit(1)\n})\n"], "mappings": ";wdACA,IAAAA,EAAkB,sBAElB,SAASC,GAAa,CACpB,IAAMC,EAAO,QAAQ,KAAK,MAAM,CAAC,EAE3BC,EADM,QAAQ,IACI,cAAgB,GAClCC,EAAc,gBAEpB,OAAID,EAAY,SAAS,MAAM,EACtB,YAAYC,IAAcF,EAAK,OAAS,IAAIA,EAAK,KAAK,GAAG,IAAM,KAC7DC,EAAY,SAAS,MAAM,EAC7B,YAAYC,IAAcF,EAAK,OAAS,IAAIA,EAAK,KAAK,GAAG,IAAM,KAC7DC,EAAY,SAAS,KAAK,EAC5B,QAAQC,IAAcF,EAAK,OAAS,IAAIA,EAAK,KAAK,GAAG,IAAM,KAE3D,OAAOE,IAAcF,EAAK,OAAS,IAAIA,EAAK,KAAK,GAAG,IAAM,IAErE,CAEA,IAAMG,EAAO,SAAY,CACvB,QAAQ,IACN,EAAAC,QAAM,OACJ,iFACF,CACF,EACA,QAAQ,IAAI,EAAE,EACd,QAAQ,IAAI,EAAAA,QAAM,MAAM,KAAKL,EAAW,GAAG,CAAC,EAC5C,QAAQ,IAAI,EAAE,EACd,QAAQ,IACN,EAAAK,QAAM,OAAO,6DAA6D,CAC5E,EACA,QAAQ,IAAI,EAAE,CAChB,EAEAD,EAAK,EAAE,MAAOE,GAAU,CACtB,QAAQ,MAAM,EAAAD,QAAM,IAAI,QAAQ,EAAGC,EAAM,OAAO,EAChD,QAAQ,KAAK,CAAC,CAChB,CAAC", "names": ["import_chalk", "getInvoker", "args", "npmExecPath", "packageName", "main", "chalk", "error"]}