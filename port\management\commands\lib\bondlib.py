import QuantLib as ql
import pandas as pd
from pandas.tseries.offsets import BDay, MonthEnd
import numpy as np
from datetime import datetime, timedelta
import holidays
from zoneinfo import ZoneInfo
from dateutil.easter import easter

from port.models import Journal, Bnr, Instrument


class EnhancedBondAccrualCalculator:

    def __init__(self):
        # self.calendar = ql.TARGET()  # European calendar
        self.day_counter = ql.Actual360() # Can be changed to other conventions
        
    paymentConvention = {
        # 'SimpleDayCounter': ql.SimpleDayCounter(),
        # '30/360': ql.Thirty360(ql.Thirty360.ISMA),
        '30/360': ql.Thirty360(ql.Thirty360.ISDA),
        'ISMA-30/360': ql.Thirty360(ql.Thirty360.European),
        'ACT/ACT': ql.ActualActual(ql.ActualActual.ISMA),
    }

    calendar_map = {
        'GovernmentBond': ql.UnitedStates(ql.UnitedStates.GovernmentBond),
        'TARGET': ql.TARGET(),
        'NYSE': ql.UnitedStates(ql.UnitedStates.NYSE),
        'FederalReserve': ql.UnitedStates(ql.UnitedStates.FederalReserve), 
        'Settlement': ql.UnitedStates(ql.UnitedStates.Settlement),
    }

    def query_data(self):
        journal_qs = Journal.objects.filter(
            operation__operation_code__contains='BOND'
            ).select_related(
                'operation',
                'instrument',
                'instrument__currency',
            ).values(
                'id',
                'ubo__ubo_code',
                'custodian__custodian_code',
                'partner__partner_code',
                'operation__operation_code',
                'instrument__symbol',
                'instrument__currency__currency_code',
                'account__account_code',
                'date',
                'value',
                'quantity',
                'bnr',
                'value_ron',
                'details',
            )
        
        df_bonds = pd.DataFrame(list(journal_qs)).rename(columns={
                'instrument__currency__currency_code': 'currency',
                'operation__operation_code': 'operation',
                'instrument__symbol': 'symbol',
                'instrument__convention': 'convention',
                'instrument__calendar': 'calendar',
                'partner__partner_code': 'partner',
                'custodian__custodian_code': 'custodian',
                'account__account_code': 'account',
                'ubo__ubo_code': 'ubo',
            })
        if len(df_bonds) > 0:
            print(f"Found {len(df_bonds)} bond operations")
            df_bonds['instrument'] = df_bonds['custodian'].astype(str) + '_' + df_bonds['symbol'].astype(str)
            # print(df_bonds)
        else:
            print("No bond operations found")

        # 2. Query BNR rates
        bnr_qs = Bnr.objects.filter(
            currency_code__currency_code__in=['EUR', 'USD', 'MXN']
            ).select_related(
                'currency_code'
            ).values(
                'currency_code__currency_code',
                'date',
                # 'value',
                'value_exact'
            )
        
        df_bnr = pd.DataFrame(list(bnr_qs)).rename(columns={
            'currency_code__currency_code': 'currency',
            # 'value': 'bnr_ieri',
            'value_exact': 'bnr',
            })

        # Remove holidays
        df_bnr = df_bnr[~df_bnr['bnr'].isna()]

        # Ensure date is datetime type
        df_bnr["date"] = pd.to_datetime(df_bnr["date"])

        # Sort by date and currency
        df_bnr.sort_values(['date', 'currency'], inplace=True)
        
        # Create month column for grouping
        df_bnr['month'] = df_bnr['date'].dt.to_period('M')

        # Get the latest date for each month and currency combination
        df_bnr_eom = df_bnr.drop_duplicates(subset=['currency', 'month'], keep='last').copy()
        
        # Drop the temporary month column if you don't need it
        df_bnr = df_bnr.drop(columns=['month', ])
        df_bnr_eom = df_bnr_eom.drop(columns=['month', ]).rename(columns={'bnr': 'bnr_eom'})


        # 3. Query Instruments for bonds
        instruments_qs = Instrument.objects.filter(
            type='BOND'
            ).select_related(
                'currency',
                'custodian'
            ).values(
                'symbol',
                'bond_issue',
                'bond_first_coupon',
                'maturity',
                'interest',
                'convention', 
                'calendar',
                'currency__currency_code',
                'bond_coupon_count',
                'face_value',
            )
        
        df_bond_info = pd.DataFrame(list(instruments_qs)).rename(columns={
                'currency__currency_code': 'currency',
            })
        if len(df_bond_info) > 0:
            print(f"Found {len(df_bond_info)} bond instruments")
        else:
            print("No bond instruments found")

        # Remove former accruals
        removed_ops = [
            'INTEREST_ACCRUAL_BOND', 'BOND_ACCRUAL_REVERSAL',
            'FX_DIF_ACCRUAL_MINUS', 'FX_DIF_ACCRUAL_PLUS',
            ]
        df_bonds = df_bonds[~df_bonds['operation'].isin(removed_ops)]

        # Remove bnr 
        df_bonds = df_bonds.drop(columns=['bnr', 'value_ron'])

        return df_bonds, df_bond_info, df_bnr, df_bnr_eom

    def get_last_trading_days(self, start_date, end_date, calendar_type='GovernmentBond'):
        """
        Calculate last trading days for each month in the given date range.
        
        Args:
            start_date: Start date to begin calculating last trading days
            end_date: End date to stop calculating last trading days  
            calendar_type: str, type of QuantLib calendar to use
            
        Returns:
            list: Last trading days for each month
        """
        # Find all accrual dates
        date_range = pd.date_range(start_date, end_date, freq='ME')
        
        # Get the appropriate calendar
        calendar = self.calendar_map.get(calendar_type, self.calendar_map['TARGET'])

        last_trading_days = []
        
        for date in date_range:
            # Convert to QuantLib date
            ql_date = ql.Date(date.day, date.month, date.year)
            
            # Adjust to last business day if end of month is not a business day
            while not calendar.isBusinessDay(ql_date):
                ql_date = calendar.adjust(ql_date, ql.Preceding)
                
            # Convert back to Python date
            py_date = datetime(ql_date.year(), ql_date.month(), ql_date.dayOfMonth()).date()
            last_trading_days.append(py_date)
        
        return last_trading_days


    # def get_coupon_schedule(self, bond_issue, bond_first_coupon, maturity, bond_coupon_count):
    #     """Get a formatted schedule of all coupon dates"""

    #     # Initialize list with first coupon date
    #     coupon_dates = [bond_first_coupon]
        
    #     # Calculate subsequent coupon dates - yearly
    #     current_date = bond_first_coupon
    #     while current_date < maturity:
    #         current_date = (current_date + pd.DateOffset(years=1)).date()
    #         if current_date > maturity: # Don't include dates after maturity
    #             break
    #         coupon_dates.append(current_date)
        
    #     if bond_coupon_count == 2:
    #         # Calculate subsequent coupon dates at each bond anniversary
    #         current_date = bond_issue 
    #         while current_date < maturity:
    #             current_date = (current_date + pd.DateOffset(years=1)).date()
    #             if current_date > maturity: # Don't include dates after maturity
    #                 break
    #             coupon_dates.append(current_date)
       
    #     coupon_dates = sorted(coupon_dates)

    #     schedule = pd.DataFrame({
    #         # 'coupon_date': coupon_dates,
    #         'last_coupon_date': [bond_issue] + coupon_dates,
    #         'coupon_number': range(0, len(coupon_dates) + 1)
    #     })

    #     schedule['next_coupon_date'] = schedule['last_coupon_date'].shift(-1)
        
    #     return schedule


    def get_coupon_schedule(self, bond_info):
        """Get a list of all coupon dates for a bond"""
        calendar = self.calendar_map.get(bond_info['calendar'], self.calendar_map['TARGET'])
        
        frequency = ql.Annual
        if bond_info['bond_coupon_count'] == 2:
            frequency = ql.Semiannual

        issue_date = ql.Date(bond_info['bond_issue'].day, 
                    bond_info['bond_issue'].month,
                    bond_info['bond_issue'].year)
        maturity_date = ql.Date(bond_info['maturity'].day,
                        bond_info['maturity'].month, 
                        bond_info['maturity'].year)
        first_coupon = ql.Date(
            bond_info['bond_first_coupon'].day, 
            bond_info['bond_first_coupon'].month,
            bond_info['bond_first_coupon'].year) 

        schedule_coupon = ql.Schedule(
            issue_date,
            maturity_date,
            ql.Period(frequency),
            calendar,
            ql.Following,    # Payment convention
            ql.Following,    # Termination convention
            ql.DateGeneration.Forward,   # Date generation
            False,   # End of month rule, True for US treasuries
            first_coupon,  # Add first coupon date
            # maturity_date
            )    

        # Create a new list with only dates >= first_coupon
        valid_dates = [d for d in schedule_coupon if d >= first_coupon]

        return schedule_coupon

    def calculate_accruals(self, date, quantity, bond_info, prev_coupon_date=None):
        """Calculate accrued interest for a bond position using specified conventions
        
        Args:
            date: Trade date
            quantity: Bond position (face value * quantity)
            bond_info: Dictionary containing bond parameters
            prev_coupon_date: Previous coupon date if known
        """
        # Convert to QuantLib date
        trade_date = ql.Date(date.day, date.month, date.year)

        # Get applicable calendar from mapping
        calendar = self.calendar_map.get(bond_info['calendar'], self.calendar_map['TARGET'])
        
        # Get payment_convention from mapping
        payment_convention = self.paymentConvention.get(bond_info['convention'], self.paymentConvention['ACT/ACT'])

        # Create schedule first to check for coupon dates
        frequency = ql.Annual
        if bond_info['bond_coupon_count'] == 2:
            frequency = ql.Semiannual
            # frequency = ql.Period('6M')

        # Convert dates to QuantLib format
        issue_date = ql.Date(bond_info['bond_issue'].day, 
                        bond_info['bond_issue'].month,
                        bond_info['bond_issue'].year)
        maturity_date = ql.Date(bond_info['maturity'].day,
                            bond_info['maturity'].month, 
                            bond_info['maturity'].year)
        bond_first_coupon = ql.Date(bond_info['bond_first_coupon'].day,
                            bond_info['bond_first_coupon'].month,   
                            bond_info['bond_first_coupon'].year)

        # Create schedule
        schedule_coupon = self.get_coupon_schedule(bond_info)
        coupon_dates = list(schedule_coupon.dates())
        # print(coupon_dates)

        settlement_days = 2

        # Adjust for reduction of settlement for USD after May 28, 2024
        new_settlement_cutoff_date = ql.Date(28, 5, 2024)
        if (bond_info['currency'] == 'USD') and (trade_date>new_settlement_cutoff_date):
            settlement_days = 1

        # Ad-hoc correction, not really correct
        if bond_info['calendar'] == 'Settlement':
            settlement_days = 1


        target_settlement_date = calendar.advance(
            trade_date,
            ql.Period(settlement_days, ql.Days),
            ql.Following
        )

        if target_settlement_date in coupon_dates:
            # If target settlement date is a coupon date, trade_date as settlemnt date
            settlement_date = trade_date
        else:
            settlement_date = target_settlement_date

        # Set evaluation date to settlement date
        ql.Settings.instance().evaluationDate = settlement_date

        # Create fixed rate bond
        coupon_rate = bond_info['interest'] / 100.0
        coupon_rates = [coupon_rate]
        
        bond = ql.FixedRateBond(
            settlement_days,  # 0 days for coupon dates
            100,                 # Face amount
            schedule_coupon,
            coupon_rates,
            payment_convention
            # ql.Thirty360(ql.Thirty360.ISMA)
            # ql.Thirty360(ql.Thirty360.European)
            # ql.ActualActual(ql.ActualActual.ISMA)
            )

        # Calculate accrued interest using settlement date
        accrued_percentage = bond.accruedAmount(settlement_date)
        
        # Convert percentage to actual amount based on quantity
        accrued_amount = (accrued_percentage / 100.0) * quantity

        print(date, quantity, accrued_amount, trade_date, settlement_date)

        return accrued_amount

 
