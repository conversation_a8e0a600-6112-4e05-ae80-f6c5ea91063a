from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.http import HttpResponse
from .models import AIHedgeFundCreds
from .serializers import (
    AIHedgeFundCredsSerializer, 
    AIHedgeFundCredsReadSerializer,
    AIHedgeFundCredsExportSerializer
)


class AIHedgeFundCredsView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get all AI Hedge Fund credentials with masked sensitive data"""
        try:
            creds = AIHedgeFundCreds.objects.all()
            serializer = AIHedgeFundCredsReadSerializer(creds, many=True)
            
            return Response({
                'ai_hedge_fund_credentials': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def post(self, request):
        """Create new AI Hedge Fund credentials"""
        try:
            serializer = AIHedgeFundCredsSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(
                    {'message': 'AI Hedge Fund credentials created successfully'},
                    status=status.HTTP_201_CREATED
                )
            return Response(
                serializer.errors,
                status=status.HTTP_400_BAD_REQUEST
            )
                
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def put(self, request):
        """Update existing AI Hedge Fund credentials"""
        try:
            cred_id = request.data.get('id')
            
            if not cred_id:
                return Response(
                    {'error': 'Credential ID is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            try:
                cred = AIHedgeFundCreds.objects.get(id=cred_id)
            except AIHedgeFundCreds.DoesNotExist:
                return Response(
                    {'error': f'AI Hedge Fund credentials with ID {cred_id} not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            serializer = AIHedgeFundCredsSerializer(cred, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(
                    {'message': 'AI Hedge Fund credentials updated successfully'},
                    status=status.HTTP_200_OK
                )
            return Response(
                serializer.errors,
                status=status.HTTP_400_BAD_REQUEST
            )
                
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def delete(self, request):
        """Delete AI Hedge Fund credentials"""
        try:
            cred_id = request.data.get('id')
            
            if not cred_id:
                return Response(
                    {'error': 'Credential ID is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            try:
                cred = AIHedgeFundCreds.objects.get(id=cred_id)
            except AIHedgeFundCreds.DoesNotExist:
                return Response(
                    {'error': f'AI Hedge Fund credentials with ID {cred_id} not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            cred.delete()
            return Response(
                {'message': 'AI Hedge Fund credentials deleted successfully'},
                status=status.HTTP_200_OK
            )
                
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AIHedgeFundEnvExportView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request, cred_id):
        """Export AI Hedge Fund credentials as .env file"""
        try:
            try:
                cred = AIHedgeFundCreds.objects.get(id=cred_id, is_active=True)
            except AIHedgeFundCreds.DoesNotExist:
                return Response(
                    {'error': f'Active AI Hedge Fund credentials with ID {cred_id} not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            serializer = AIHedgeFundCredsExportSerializer(cred)
            env_vars = serializer.data
            
            # Generate .env file content
            env_content = "# AI Hedge Fund Environment Variables\n"
            env_content += f"# Generated from credentials: {cred.name}\n"
            env_content += f"# Generated at: {cred.updated_at.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            
            for key, value in env_vars.items():
                if value:  # Only include non-empty values
                    env_content += f"{key}={value}\n"
            
            # Return as downloadable file
            response = HttpResponse(env_content, content_type='text/plain')
            response['Content-Disposition'] = f'attachment; filename="ai_hedge_fund_{cred.name.lower().replace(" ", "_")}.env"'
            
            return response
                
        except Exception as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
