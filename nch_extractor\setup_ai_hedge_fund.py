#!/usr/bin/env python
"""
Setup script for AI Hedge Fund credentials integration
"""
import os
import sys
import django
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core_extractor.settings')
django.setup()

from ai_hedge_fund_provider.models import AIHedgeFundCreds


def main():
    print("🚀 AI Hedge Fund Credentials Setup")
    print("=" * 50)
    
    # Check if any credentials already exist
    existing_count = AIHedgeFundCreds.objects.count()
    if existing_count > 0:
        print(f"ℹ️  Found {existing_count} existing AI hedge fund credential set(s)")
        
        response = input("Do you want to create a new credential set? (y/n): ")
        if response.lower() != 'y':
            print("Setup cancelled.")
            return
    
    print("\n📝 Creating new AI hedge fund credentials...")
    
    # Get basic information
    name = input("Enter a name for this credential set (default: 'Production'): ").strip()
    if not name:
        name = "Production"
    
    description = input("Enter a description (optional): ").strip()
    if not description:
        description = f"AI hedge fund credentials for {name}"
    
    print(f"\n🔑 Enter your API keys (leave blank to skip):")
    
    # Collect API keys
    api_keys = {}
    
    providers = [
        ('openai_api_key', 'OpenAI API Key (sk-...)', 'OPENAI_API_KEY'),
        ('anthropic_api_key', 'Anthropic API Key (sk-ant-...)', 'ANTHROPIC_API_KEY'),
        ('groq_api_key', 'Groq API Key (gsk_...)', 'GROQ_API_KEY'),
        ('google_api_key', 'Google API Key', 'GOOGLE_API_KEY'),
        ('deepseek_api_key', 'DeepSeek API Key', 'DEEPSEEK_API_KEY'),
        ('financial_datasets_api_key', 'Financial Datasets API Key', 'FINANCIAL_DATASETS_API_KEY'),
    ]
    
    for field_name, prompt_text, env_var in providers:
        # Check if environment variable exists
        env_value = os.getenv(env_var)
        if env_value:
            use_env = input(f"{prompt_text} (found in {env_var}, use it? y/n): ")
            if use_env.lower() == 'y':
                api_keys[field_name] = env_value
                continue
        
        # Get from user input
        value = input(f"{prompt_text}: ").strip()
        if value:
            api_keys[field_name] = value
    
    # Optional OpenAI base URL
    openai_base = input("OpenAI API Base URL (optional, default: https://api.openai.com/v1): ").strip()
    if openai_base and openai_base != "https://api.openai.com/v1":
        api_keys['openai_api_base'] = openai_base
    
    # Check if at least one API key was provided
    if not any(key.endswith('_api_key') and key in api_keys for key in api_keys):
        print("❌ Error: At least one API key is required!")
        return
    
    # Create the credentials
    try:
        creds = AIHedgeFundCreds.objects.create(
            name=name,
            description=description,
            is_active=True,
            **api_keys
        )
        
        print(f"\n✅ Successfully created AI hedge fund credentials!")
        print(f"   Name: {creds.name}")
        print(f"   ID: {creds.id}")
        print(f"   Available providers: {', '.join(creds.get_available_providers())}")
        print(f"   Financial data access: {'Yes' if creds.has_financial_data_access() else 'No'}")
        
        print(f"\n📋 Next steps:")
        print(f"   1. Access the web interface to manage credentials")
        print(f"   2. Export .env file for your AI hedge fund application")
        print(f"   3. Use the credentials in your trading algorithms")
        
        # Offer to export .env file
        export_env = input(f"\nWould you like to export a .env file now? (y/n): ")
        if export_env.lower() == 'y':
            export_env_file(creds)
            
    except Exception as e:
        print(f"❌ Error creating credentials: {e}")


def export_env_file(creds):
    """Export credentials to .env file"""
    from ai_hedge_fund_provider.serializers import AIHedgeFundCredsExportSerializer
    
    serializer = AIHedgeFundCredsExportSerializer(creds)
    env_vars = serializer.data
    
    filename = f"ai_hedge_fund_{creds.name.lower().replace(' ', '_')}.env"
    
    with open(filename, 'w') as f:
        f.write("# AI Hedge Fund Environment Variables\n")
        f.write(f"# Generated from credentials: {creds.name}\n")
        f.write(f"# Generated at: {creds.updated_at.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for key, value in env_vars.items():
            if value:  # Only include non-empty values
                f.write(f"{key}={value}\n")
    
    print(f"📄 Exported .env file: {filename}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Setup cancelled by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
