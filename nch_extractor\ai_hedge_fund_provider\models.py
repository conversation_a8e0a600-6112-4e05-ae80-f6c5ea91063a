from django.db import models
from encrypted_model_fields.fields import EncryptedCharField


class AIHedgeFundCreds(models.Model):
    """Model to store AI Hedge Fund API credentials securely"""
    
    # Basic info
    name = models.CharField(max_length=100, help_text="Friendly name for this credential set")
    description = models.TextField(blank=True, help_text="Optional description")
    is_active = models.BooleanField(default=True, help_text="Whether these credentials are active")
    
    # AI Provider API Keys (encrypted)
    anthropic_api_key = EncryptedCharField(
        max_length=255, 
        blank=True, 
        null=True,
        help_text="Anthropic API key for Claude models"
    )
    
    deepseek_api_key = EncryptedCharField(
        max_length=255, 
        blank=True, 
        null=True,
        help_text="DeepSeek API key for DeepSeek models"
    )
    
    groq_api_key = EncryptedCharField(
        max_length=255, 
        blank=True, 
        null=True,
        help_text="Groq API key for Llama and other models"
    )
    
    google_api_key = Encrypted<PERSON>har<PERSON>ield(
        max_length=255, 
        blank=True, 
        null=True,
        help_text="Google API key for Gemini models"
    )
    
    openai_api_key = EncryptedCharField(
        max_length=255, 
        blank=True, 
        null=True,
        help_text="OpenAI API key for GPT models"
    )
    
    # Financial data API key
    financial_datasets_api_key = EncryptedCharField(
        max_length=255, 
        blank=True, 
        null=True,
        help_text="Financial Datasets API key for market data"
    )
    
    # Optional OpenAI base URL for custom endpoints
    openai_api_base = models.URLField(
        blank=True, 
        null=True,
        help_text="Custom OpenAI API base URL (optional)"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'ai_hedge_fund_creds'
        verbose_name = 'AI Hedge Fund Credentials'
        verbose_name_plural = 'AI Hedge Fund Credentials'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({'Active' if self.is_active else 'Inactive'})"
    
    def get_available_providers(self):
        """Return list of providers that have API keys configured"""
        providers = []
        if self.anthropic_api_key:
            providers.append('Anthropic')
        if self.deepseek_api_key:
            providers.append('DeepSeek')
        if self.groq_api_key:
            providers.append('Groq')
        if self.google_api_key:
            providers.append('Google')
        if self.openai_api_key:
            providers.append('OpenAI')
        return providers
    
    def has_financial_data_access(self):
        """Check if financial data API key is configured"""
        return bool(self.financial_datasets_api_key)
